<template>
  <n-space vertical>
    <n-spin :show="loadingShow" :delay="1000">
      <n-card :bordered="false">
        <BasicForm
          :showSlotConfig="false"
          @register="register"
          @submit="handleSubmit"
          @reset="handleReset"
        >
          <template #statusSlot="{ model, field }">
            <n-input v-model:value="model[field]" />
          </template>
        </BasicForm>
      </n-card>
      <n-card :bordered="false" class="mt-3">
        <BasicTable
          :columns="columns"
          :request="loadDataTable"
          :row-key="rowKey"
          ref="actionRef"
          :actionColumn="actionColumn"
          :scroll-x="1090"
          :striped="true"
          :remote="false"
        >
          <template #tableTitle>
            <n-button v-permission="{ action: 'add' }" type="primary" @click="addTable">
              <template #icon>
                <n-icon>
                  <PlusOutlined />
                </n-icon>
              </template>
              新建
            </n-button>
          </template>
          <template #toolbar> </template>
        </BasicTable>
      </n-card>
      <FormDialog
        v-model="dialogVisible"
        :title="modelTitle"
        :formModel="formVal"
        :labelWidth="140"
        @submit="submitMenu"
        @close="dialogVisible = false"
      >
        <EditMenuForm
          @update:form-val="formVal = $event"
          :formVal="formVal"
          :menuList="allMenuList"
          :isType="isType"
        />
      </FormDialog>
    </n-spin>
  </n-space>
</template>
<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { h, ref, reactive, onMounted } from 'vue';
  import { getMenuList, add_menu, put_menu, del_menu } from '@/api/system/menu';
  import { BasicTable, TableAction } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form/index';
  import { PlusOutlined } from '@vicons/antd';
  import { columns } from './columns';
  import EditMenuForm from './components/EditMenuForm.vue';
  import { useMessage, useDialog } from 'naive-ui';
  import { useUser } from '@/store/modules/user';

  interface RowData {
    title: string;
    id: string;
    children?: RowData[];
  }
  const rowKey = (row: RowData) => row.id;
  const actionRef = ref();
  const modelTitle = ref('');
  const formVal = ref({});
  const menuList = ref([]);
  const allMenuList = ref([]);
  const dialogVisible = ref(false);
  const isType = ref('add');
  const loadingShow = ref(false);
  const schemas: FormSchema[] = [
    {
      field: 'title',
      component: 'NInput',
      label: '菜单名',
      componentProps: {
        placeholder: '请输入菜单名',
        onInput: (e: any) => {
          console.log(e);
        },
      },
    },
  ];
  const userStore = useUser();

  // dialog提示窗
  const dialog = useDialog();
  const message = useMessage();

  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    showAdvancedButton: false,
    schemas,
  });
  onMounted(() => {
    getMenuList().then((res) => {
      allMenuList.value = res.data;
    });
  });
  const loadDataTable = async (res) => {
    return await getMenuList({ ...getFieldsValue(), ...res }).then((result: any) => {
      menuList.value = result.data;
      return {
        total: result.data.length,
        records: result.data,
      };
    });
  };
  const actionColumn = reactive({
    width: 180,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction as any, {
        style: 'button',
        actions: [
          {
            label: '删除',
            onClick: handleDelete.bind(null, record),
            ifShow: true,
            buttonsAuth: 'delete',
          },
          {
            label: '编辑',
            onClick: handleEdit.bind(null, record),
            ifShow: () => {
              return true;
            },
            buttonsAuth: 'edit',
          },
        ],
      });
    },
  });
  function handleEdit(record) {
    modelTitle.value = '编辑菜单';
    formVal.value = record;
    isType.value = 'edit';
    dialogVisible.value = true;
  }

  function handleDelete(record) {
    dialog.warning({
      title: '操作提示',
      content: `是否确认删除?`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        del_menu({ id: record.id }).then(({ code }) => {
          if (code === 200) {
            reloadTable();
            message.success('删除成功');
          }
        });
      },
    });
  }

  function reloadTable() {
    actionRef.value.reload();
  }
  function addTable() {
    modelTitle.value = '新增菜单';
    isType.value = 'add';
    formVal.value = {};
    dialogVisible.value = true;
  }
  function handleSubmit() {
    reloadTable();
  }

  function handleReset() {
    reloadTable();
  }
  function submitMenu(formVal, formDone) {
    const api = isType.value === 'add' ? add_menu : put_menu;
    api(formVal)
      .then((res) => {
        if (res.code === 200) {
          window['$message'].success('操作成功');
          dialogVisible.value = false;
          if (isType.value === 'edit') {
            userStore.getInfo();
            setTimeout(() => {
              window.location.reload();
            }, 500);
          } else {
            reloadTable();
          }
        }
      })
      .finally(() => {
        formDone?.();
      });
  }
</script>
