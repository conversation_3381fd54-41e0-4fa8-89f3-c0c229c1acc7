import { defineStore } from 'pinia';
import { store } from '@/store';
import {
  ACCESS_TOKEN,
  CURRENT_USER,
  IS_SCREENLOCKED,
  BUTTONS_DATA,
  MENU_DATA,
} from '@/store/mutation-types';
import { ResultEnum } from '@/enums/httpEnum';

import { getUserInfo as getUserInfoApi, login } from '@/api/system/user';
import { storage } from '@/utils/Storage';
import { formatMenu } from '@/utils';
export type UserInfoType = {
  username: string;
  email?: string;
  id?: number;
  mobileNo?: number | string;
  adminBranchName?: string;
  roleName?: string;
};

export interface IUserState {
  token: string;
  username: string;
  welcome: string;
  avatar: string;
  permissions: any[];
  info: UserInfoType;
  buttons: string[];
  menus: any[];
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): IUserState => ({
    token: storage.get(ACCESS_TOKEN, ''),
    username: '',
    welcome: '',
    avatar: '',
    permissions: [],
    info: storage.get(CURRENT_USER, {}),
    buttons: storage.get(BUTTONS_DATA, []),
    menus: storage.get(MENU_DATA, []),
  }),
  getters: {
    getToken(): string {
      return this.token;
    },
    getAvatar(): string {
      return this.avatar;
    },
    getNickname(): string {
      return this.username;
    },
    getPermissions(): [any][] {
      return this.permissions;
    },
    getUserInfo(): UserInfoType {
      return this.info;
    },
    getButtons(): string[] {
      return this.buttons;
    },
    getMenus(): [any][] {
      return this.menus;
    },
  },
  actions: {
    setToken(token: string) {
      this.token = token;
    },
    setAvatar(avatar: string) {
      this.avatar = avatar;
    },
    setPermissions(permissions) {
      this.permissions = permissions;
    },
    setUserInfo(info: UserInfoType) {
      this.info = info;
    },
    setButtons(buttons) {
      const ex = 7 * 24 * 60 * 60;
      storage.set(BUTTONS_DATA, buttons, ex);
      this.buttons = buttons;
    },
    setMenus(menu) {
      const ex = 7 * 24 * 60 * 60;
      storage.set(MENU_DATA, menu, ex);
      this.buttons = menu;
    },
    // 登录
    async login(params: any) {
      const response = await login(params);
      const { data, code } = response;
      if (code === ResultEnum.SUCCESS) {
        const menus = formatMenu(data.menus);
        const ex = 7 * 24 * 60 * 60;
        storage.set(ACCESS_TOKEN, data.access_token, ex);
        storage.set(CURRENT_USER, data, ex);
        storage.set(IS_SCREENLOCKED, false);
        this.setButtons(data.buttons);
        this.setMenus(menus);
        this.setToken(data.access_token);
        this.setUserInfo(data);
      }
      return response;
    },

    // 获取用户信息
    async getInfo() {
      const response = await getUserInfoApi();
      const { data, code } = response;

      if (code !== ResultEnum.SUCCESS) {
        throw new Error('获取用户信息失败');
      }

      const permissionsList = data.permissions;
      this.setPermissions(permissionsList);
      this.setUserInfo(data);
      const formatMenuData = formatMenu(data.menus);
      this.setMenus(formatMenuData);
      this.setButtons(data.buttons);
      this.setAvatar(data.avatar);
      return data;
    },

    // 登出
    async logout() {
      this.setPermissions([]);
      this.setMenus([]);
      this.setButtons([]);
      this.setUserInfo({ username: '', email: '' });
      storage.remove(ACCESS_TOKEN);
      storage.remove(CURRENT_USER);
    },
  },
});

// Need to be used outside the setup
export function useUser() {
  return useUserStore(store);
}
