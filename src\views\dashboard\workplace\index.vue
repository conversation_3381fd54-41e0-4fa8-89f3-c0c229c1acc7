<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable(), resetDateQuery()"
    >
      <!-- <template #blongUserId="{ model, field }">
        <NSelect
          v-model:value="model[field]"
          label-field="username"
          value-field="id"
          :options="userList"
          placeholder="请选择归属人"
          filterable
        />
      </template> -->
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="tableColumns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      :scroll-x="2200"
      ref="action"
      :striped="true"
    >
      <template #tableTitle>
        <n-space :wrap-item="false" align="center">
          <n-tabs
            class="w-[230px] mr-5"
            :value="tabValue"
            type="line"
            animated
            @update:value="
              (val) => {
                (tabValue = val), reloadTable();
              }
            "
          >
            <!-- followStatusOptions 按照1,0,2排序 -->
            <n-tab-pane
              v-for="item in followStatusOptions.sort(
                (a, b) => [1, 0, 2].indexOf(a.value) - [1, 0, 2].indexOf(b.value)
              )"
              :name="item.value"
              :tab="item.label"
              :key="item.value"
            />
          </n-tabs>
          <n-button v-permission="{ action: 'add' }" type="primary" @click="handleOpenForm">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增线索
          </n-button>
          <n-button v-permission="{ action: 'claim' }" type="primary" @click="handleOpenClueClaim">
            认领线索
          </n-button>
        </n-space>
      </template>
    </BasicTable>
  </n-card>

  <FormModal ref="formModal" @close="reloadTable" />
  <ClueClaim ref="clueClaim" @close="reloadTable" />
  <UpdateCustomerModal ref="updateCustomerModalRef" @success="reloadTable" />
  <AddFollowUpDrawer ref="addFollowUpDrawer" @submit-success="reloadTable" />
  <DetailDrawer ref="detailDrawer" @close="reloadTable" />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { reactive, useTemplateRef, ref, onMounted, computed } from 'vue';
  import { PlusOutlined, HeartFilled, HeartOutlined } from '@vicons/antd';
  import FormModal from '@/views/client/myClients/FormModal.vue';
  import { insertColumnsAt } from '@/utils/tableUtils';
  import ClueClaim from '@/views/client/myClients/ClueClaim.vue';
  import UpdateCustomerModal from './components/UpdateCustomerModal/UpdateCustomerModal.vue';
  import DetailDrawer from '@/components/DetailDrawer/index.vue';
  import AddFollowUpDrawer from '@/components/DetailDrawer/components/AddFollowUpDrawer.vue';
  import {
    FollowStatusOptions,
    ContactStatusOptions,
    IntentionDegreeOptions,
    IsAddWeChatOptions,
    MediaSourceOptions,
  } from '@/views/client/enum';
  import {
    getFollowUpListApi,
    exportFollowUpListApi,
    clueLikeApi,
  } from '@/api/dashboard/workplace';
  import { getUserListApi } from '@/api/global';
  import dayjs from 'dayjs';
  import { useUser } from '@/store/modules/user';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { getChannelListApi } from '@/api/client';
  import { handleDateClearStatus } from './utils/date';
  import { promisifyDialog } from '@/utils/nativeUtils';

  const userStore = useUser();
  const detailDrawerRef = useTemplateRef<InstanceType<typeof DetailDrawer>>('detailDrawer');
  const addFollowUpDrawerRef =
    useTemplateRef<InstanceType<typeof AddFollowUpDrawer>>('addFollowUpDrawer');
  const updateCustomerModalRef = ref<InstanceType<typeof UpdateCustomerModal> | null>(null);
  const tabValue = ref('1');
  const exportLoading = ref(false);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const clueClaimRef = useTemplateRef<InstanceType<typeof ClueClaim>>('clueClaim');
  const formModalRef = useTemplateRef<InstanceType<typeof FormModal>>('formModal');
  const followStatusOptions = ref(FollowStatusOptions);
  // 归属人list
  // const userList = ref<any[]>([]);
  const channelList = ref<any[]>([]);
  const dateQuery = ref<any>({
    createTimeQuery: [
      dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    triageTimeQuery: [],
    lastFollowTimeQuery: [],
  });
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'createTimeQuery',
        label: '创建时间',
        component: 'NDatePicker',
        childKey: ['startTime', 'endTime'],
        defaultValue: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'createTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.createTimeQuery = value;
          },
        },
      },
      {
        field: 'triageTimeQuery',
        label: '分发时间',
        component: 'NDatePicker',
        childKey: ['triageStartTime', 'triageEndTime'],
        // defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'triageTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.triageTimeQuery = value;
          },
        },
      },
      {
        field: 'lastFollowTimeQuery',
        label: '跟进时间',
        component: 'NDatePicker',
        childKey: ['lastFollowStartTime', 'lastFollowEndTime'],
        // defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'lastFollowTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.lastFollowTimeQuery = value;
          },
        },
      },
      {
        field: 'name',
        component: 'NInput',
        label: '姓名',
        componentProps: {
          placeholder: '请输入姓名',
        },
      },
      {
        field: 'mobileNo',
        component: 'NInput',
        label: '电话',
        componentProps: {
          placeholder: '请输入电话号码',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { mobileNo } = getFieldsValue();
            const formattedValue = mobileNo.replace(/\D/g, '');
            if (mobileNo !== formattedValue) {
              setFieldsValue({ mobileNo: formattedValue });
            }
          },
        },
      },
      // {
      //   field: 'blongUserId',
      //   label: '归属人',
      //   slot: 'blongUserId',
      // },
      {
        field: 'channelCode',
        component: 'NSelect',
        label: '线索来源',
        componentProps: {
          placeholder: '请选择线索来源',
          options: channelList.value,
          multiple: true,
        },
      },
      {
        field: 'sourceMedia',
        component: 'NSelect',
        label: '来源媒体',
        componentProps: {
          placeholder: '请选择来源媒体',
          options: MediaSourceOptions,
          multiple: true,
        },
      },
      {
        field: 'communicationStatus',
        component: 'NSelect',
        label: '通讯状态',
        componentProps: {
          placeholder: '请选择通讯状态',
          options: ContactStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'intent',
        component: 'NSelect',
        label: '意向度',
        componentProps: {
          placeholder: '请选择意向度',
          options: IntentionDegreeOptions,
          multiple: true,
        },
      },
      {
        field: 'addWeChat',
        component: 'NSelect',
        label: '是否加微',
        componentProps: {
          placeholder: '请选择是否加微',
          options: IsAddWeChatOptions,
        },
      },
    ];
  });
  const [register, { getFieldsValue, setFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });
  const actionColumn = reactive({
    width: 180,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-space>
          <n-button
            v-permission={{ action: 'follow_up' }}
            type="primary"
            text
            onClick={() => {
              addFollowUpDrawerRef.value?.openDrawer(record.publicClueId);
            }}
          >
            跟进
          </n-button>
          <n-button
            v-permission={{ action: 'detail' }}
            type="primary"
            text
            onClick={() => {
              detailDrawerRef.value?.openDrawer(record.publicClueId);
            }}
          >
            详情
          </n-button>
          <n-button
            v-permission={{ action: 'update_info' }}
            type="primary"
            text
            onClick={() => {
              updateCustomerModalRef.value!.openModal(record.publicClueId);
            }}
          >
            补充资料
          </n-button>
        </n-space>
      );
    },
  });

  // 生成最终的表格列配置 - 在第2个位置和第7个位置插入列
  const tableColumns = insertColumnsAt(columns, {
    at: 0, // 第二个位置（索引从0开始）
    column: {
      title: '特别关注',
      key: 'likeTime',
      width: 80,
      align: 'center',
      render: (_record: any) => {
        return (
          <n-icon
            size={20}
            onClick={async () => {
              if (_record.likeTime) {
                const result = await promisifyDialog(window.$dialog.warning)({
                  title: '取消关注确认',
                  content: '请确认是否取消关注该客户,取消关注后,该客户将恢复默认排序',
                  positiveText: '确定',
                  negativeText: '取消',
                  maskClosable: false,
                });
                if (result.source !== 'positive') {
                  return;
                }
              }

              window.$loading.start();
              clueLikeApi({
                clueId: _record.publicClueId,
                likeStatus: _record.likeTime ? 0 : 1,
              })
                .then(() => {
                  _record.likeTime = _record.likeTime ? 0 : 1;
                  reloadTable();

                  if (_record.likeTime) {
                    window.$message.success('特别关注成功，已置顶显示');
                  } else {
                    window.$message.success('取消关注成功');
                  }
                })
                .finally(() => {
                  window.$loading.finish();
                });
            }}
          >
            {_record.likeTime ? <HeartFilled color="red" /> : <HeartOutlined color="grey" />}
          </n-icon>
        );
      },
    },
  });

  const loadDataTable = async (params) => {
    const { data } = await getFollowUpListApi({
      ...getFieldsValue(),
      ...params,
      followStatus: tabValue.value,
    });
    return data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  function handleOpenForm() {
    formModalRef.value!.formDialogModel = true;
  }
  function handleExport() {
    try {
      exportLoading.value = true;
      window.open(
        exportFollowUpListApi({
          ...getFieldsValue(),
          pageNumber: 1,
          pageSize: 10000,
          token: userStore.getToken,
          followStatus: tabValue.value,
        })
      );
    } finally {
      exportLoading.value = false;
    }
  }
  function handleOpenClueClaim() {
    clueClaimRef.value!.formDialogModel = true;
  }

  // async function getUserList() {
  //   try {
  //     const { data } = await getUserListApi();

  //     userList.value = data;
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }

  async function getChannelList() {
    try {
      const { data } = await getChannelListApi();

      channelList.value = data.map((item) => ({
        label: item.channelName,
        value: item.channelCode,
      }));
    } catch (err) {
      console.log(err);
    }
  }
  function resetDateQuery() {
    dateQuery.value = {
      createTimeQuery: [
        dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      triageTimeQuery: [],
      lastFollowTimeQuery: [],
    };
  }

  onMounted(() => {
    // getUserList();
    getChannelList();
  });
</script>

<style lang="less" scoped>
  :deep(.n-tabs) {
    .n-tab-pane {
      padding: 0 !important;
    }
  }
</style>
