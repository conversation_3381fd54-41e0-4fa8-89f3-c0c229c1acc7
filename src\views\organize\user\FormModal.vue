<template>
  <FormDialog
    v-model="formDialogModel"
    :title="formDetail.id ? '编辑用户' : '新增用户'"
    :form-model="paramForm"
    ref="formDialog"
    :rules="rules"
    @submit="handleSubmit"
    @close="handleClose"
    @open="handleOpen"
  >
    <n-form-item label="姓名：" path="username">
      <n-input v-model:value="paramForm.username" placeholder="请输入姓名" clearable />
    </n-form-item>
    <n-form-item label="电话号码：" path="mobileNo">
      <n-input
        v-model:value="paramForm.mobileNo"
        maxlength="11"
        clearable
        placeholder="请输入11位电话号"
      />
    </n-form-item>
    <n-form-item label="角色：" path="roleId">
      <n-select
        v-model:value="paramForm.roleId"
        placeholder="请选择角色"
        :options="roleList"
        label-field="name"
        value-field="id"
      />
    </n-form-item>
    <n-form-item label="所属部门：" path="adminBranch">
      <n-tree-select
        v-model:value="paramForm.adminBranch"
        label-field="branchName"
        key-field="id"
        children-field="childrenBranch"
        :options="deptTree"
        placeholder="请选择上级部门"
        @update:value="get_DirectSuperior"
      />
    </n-form-item>
    <n-form-item label="直属上级：" path="directSuperior">
      <n-select
        v-model:value="paramForm.directSuperior"
        placeholder="请选择直属上级"
        :options="parentOptions"
        label-field="username"
        value-field="id"
      />
    </n-form-item>
    <n-form-item label="是否启用：">
      <n-radio-group v-model:value="paramForm.enable" name="enable">
        <n-space>
          <n-radio :key="0" :value="0"> 启用 </n-radio>
          <n-radio :key="1" :value="1"> 禁用 </n-radio>
        </n-space>
      </n-radio-group>
    </n-form-item>
  </FormDialog>
</template>

<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { reactive, ref, useTemplateRef, nextTick, defineEmits } from 'vue';
  import rules from './rules';
  import { addUser, editUser, getDirectSuperior } from '@/api/system';
  import { UserInfoType } from '@/store/modules/user';
  const props = defineProps({
    roleList: {
      type: Array,
      default: () => [],
    },
    deptTree: {
      type: Array,
      default: () => [],
    },
    formDetail: {
      type: Object,
      default: () => ({}),
    },
    deptId: {
      type: [Number, String],
      default: null,
    },
  });
  const formDialogRef = useTemplateRef<InstanceType<typeof FormDialog>>('formDialog');
  interface formDataType {
    id?: number | null;
    username: string;
    mobileNo: string;
    roleId: string | number;
    adminBranch: string | number;
    directSuperior: string | number;
    enable: number;
  }
  const formState: formDataType = {
    id: null,
    username: '',
    mobileNo: '',
    roleId: '',
    adminBranch: '',
    directSuperior: '',
    enable: 0,
  };
  const paramForm: formDataType = reactive({ ...formState });
  const formDialogModel = ref(false);
  const emit = defineEmits(['submit-success']);
  // 直属上级
  const parentOptions = ref([]);
  function get_DirectSuperior() {
    getDirectSuperior({ branchId: paramForm.adminBranch }).then((res) => {
      parentOptions.value = (res?.data || []).filter(
        (item: UserInfoType) => item.id !== paramForm.id
      );
    });
  }
  const useHttpInterface = async (params) => {
    let api = params.id ? editUser : addUser;
    return await api(params);
  };
  const handleOpen = async () => {
    if (props.formDetail.id || props.formDetail.id === 0) {
      const getDetail = () =>
        nextTick(() => {
          formDialogRef.value
            ?.loadFormData(
              () =>
                new Promise((resolve) =>
                  resolve({
                    code: 200,
                    data: {
                      ...props.formDetail,
                      enable: props.formDetail.locked,
                      adminBranch: props.formDetail.branchId,
                    },
                  })
                )
            )
            .then(() => {
              return Promise.resolve();
            });
        });
      await getDetail();
    } else {
      paramForm.adminBranch = props.deptId;
    }
    get_DirectSuperior();
  };
  const handleSubmit = async (params, done) => {
    try {
      await useHttpInterface(params);
      window.$message.success(params.id ? '编辑成功' : '保存成功');
      formDialogModel.value = false;
      emit('submit-success');
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(paramForm, formState);
  };

  defineExpose({
    formDialogModel,
  });
</script>
