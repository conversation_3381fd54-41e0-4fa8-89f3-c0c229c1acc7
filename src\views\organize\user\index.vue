<template>
  <div class="flex gap-[10px] w-full user-management">
    <!--    左侧菜单-->
    <n-card class="w-[18%] min-w-[200px]" content-style="padding: 20px 10px">
      <div class="flex justify-center">
        <n-button
          v-permission="{ action: 'organize_department_add' }"
          @click="handleOpenDeptForm(null)"
        >
          <template #icon>
            <n-icon>
              <PlusOutlined />
            </n-icon>
          </template>
          添加部门
        </n-button>
      </div>
      <div class="mt-[10px]">
        <n-spin :show="deptLoading">
          <n-tree
            :data="deptOptions"
            :render-label="deptRenderLabel"
            :on-update:selected-keys="getCheckedData"
            children-field="childrenBranch"
            key-field="id"
            label-field="branchName"
            :indent="12"
            :cancelable="false"
            :selected-keys="[branchId]"
          />
        </n-spin>
      </div>
    </n-card>
    <div class="flex-1 max-w-[80%]">
      <n-card :bordered="false">
        <BasicForm
          :showSlotConfig="false"
          :enable-cache="true"
          @register="register"
          @submit="reloadTable"
          @reset="reloadTable"
        >
          <template #roleId="{ model, field }">
            <NSelect
              v-model:value="model[field]"
              label-field="name"
              value-field="id"
              :options="roleOptions"
              placeholder="请选择角色"
              filterable
              clearable
            />
          </template>
        </BasicForm>
      </n-card>
      <n-card :bordered="false" class="mt-3">
        <BasicTable
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row:ListData) => row.id"
          :actionColumn="actionColumn"
          :scroll-x="1090"
          ref="action"
          :striped="true"
        >
          <template #tableTitle>
            <n-button
              v-permission="{ action: 'organize_department_add' }"
              type="primary"
              @click="handleOpenForm"
            >
              <template #icon>
                <n-icon>
                  <PlusOutlined />
                </n-icon>
              </template>
              添加用户
            </n-button>
          </template>
        </BasicTable>
      </n-card>
    </div>
  </div>

  <FormModal
    ref="formModal"
    :role-list="roleOptions"
    :dept-tree="deptOptions"
    :deptId="branchId"
    :formDetail="userFormDetail"
    @submit-success="reloadTable"
  />
  <DeptFormModal
    ref="deptFormModal"
    :form-id="deptId"
    :dept-tree="deptOptions"
    @close="getSystemDeptTree"
  />
  <!--移交-->
  <MigrateTable ref="migrateTable" :userId="userId" :dept-tree="deptOptions" />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { PlusOutlined } from '@vicons/antd';
  import { BasicTable } from '@/components/Table';
  import { reactive, useTemplateRef, ref, onMounted } from 'vue';
  import FormModal from './FormModal.vue';
  import { DeleteOutlined, EditOutlined } from '@vicons/antd';
  import { useDialog } from 'naive-ui';
  import DeptFormModal from './DeptFormModal.vue';
  import {
    deleteSystemDeptApi,
    getSystemDeptTreeApi,
    getSystemUserListApi,
    enableUser,
  } from '@/api/system';
  import { UserStatusList } from '@/enums';
  import { get_admin_role_list } from '@/api/system/role';
  import MigrateTable from './MigrateTable.vue';

  // 选中用户id
  const userId = ref<number>();
  const roleOptions = ref([]);
  const migrateTableRef = useTemplateRef<InstanceType<typeof MigrateTable>>('migrateTable');
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  // 当前部门id
  const branchId = ref('');
  const deptLoading = ref(false);
  const deptId = ref<number | undefined>(undefined);
  const dialog = useDialog();
  const deptFormModalRef = useTemplateRef<InstanceType<typeof DeptFormModal>>('deptFormModal');
  const formModalRef = useTemplateRef<InstanceType<typeof FormModal>>('formModal');
  const schemas: FormSchema[] = [
    {
      field: 'account',
      component: 'NInput',
      label: '用户信息',
      componentProps: {
        placeholder: '请输入手机号/姓名',
      },
    },
    {
      field: 'roleId',
      label: '角色',
      slot: 'roleId',
    },
    {
      field: 'status',
      component: 'NSelect',
      label: '状态',
      componentProps: {
        placeholder: '请选择状态',
        options: UserStatusList,
      },
    },
  ];
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });
  const deptOptions = ref([]);
  const userFormDetail = ref({});
  const actionColumn = reactive({
    width: 160,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-space>
          <n-button
            type="primary"
            text
            v-permission={{ action: 'organize_user_edit' }}
            onClick={() => {
              userFormDetail.value = { ...record };
              formModalRef.value!.formDialogModel = true;
            }}
          >
            编辑
          </n-button>
          <n-button
            v-permission={{ action: 'organize_user_status' }}
            type="primary"
            text
            onClick={() => {
              changeUserStatus(record.id, record.locked === 0 ? '禁用' : '启用');
            }}
          >
            {record.locked === 0 ? '禁用' : '启用'}
          </n-button>
          <n-button
            v-permission={{ action: 'organize_user_hand_over' }}
            type="primary"
            text
            onClick={() => {
              handleOpenMigrateTable(record.id);
            }}
          >
            移交
          </n-button>
        </n-space>
      );
    },
  });
  function changeUserStatus(id, tips) {
    dialog.warning({
      title: '提示',
      content: '你确定要' + tips + '此用户？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        enableUser(id).then((res) => {
          if (res.code === 200) {
            window.$message.success('操作成功');
            reloadTable();
          }
        });
      },
    });
  }

  const handleOpenMigrateTable = (id) => {
    userId.value = id;
    migrateTableRef.value!.dialogVisible = true;
  };
  const loadDataTable = async (params) => {
    if (!branchId.value) {
      return {
        records: [],
        total: 0,
      };
    }
    const { data } = await getSystemUserListApi({
      ...getFieldsValue(),
      ...params,
      branchId: branchId.value,
    });
    return data;
  };
  function handleOpenForm() {
    userFormDetail.value = {};
    formModalRef.value!.formDialogModel = true;
  }
  function deptRenderLabel(options: any) {
    const { option } = options;
    return (
      <div class="flex justify-between w-full">
        <div>{option.branchName}</div>
        <div class="flex gap-[10px]">
          <n-button
            v-permission={{ action: 'organize_department_edit' }}
            class="text-[16px]"
            text
            on-click={(e) => {
              e.stopPropagation();
              handleOpenDeptForm(option.id);
            }}
          >
            <n-icon>
              <EditOutlined />
            </n-icon>
          </n-button>
          <n-button
            v-permission={{ action: 'organize_department_delete' }}
            class="text-[16px]"
            text
            on-click={(e) => {
              e.stopPropagation();
              handleDelete(option);
            }}
          >
            <n-icon>
              <DeleteOutlined />
            </n-icon>
          </n-button>
        </div>
      </div>
    );
  }
  function getCheckedData(options) {
    branchId.value = options[0];
    reloadTable();
  }
  function handleDelete(option) {
    dialog.warning({
      title: '提示',
      content: `确认删除此部门？(${option.branchName})`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await deleteSystemDeptApi(option.id);
        // reloadTable();
        window.$message.success('删除成功');
        getSystemDeptTree();
      },
    });
  }
  function reloadTable() {
    actionRef.value!.reload();
  }
  function handleOpenDeptForm(id) {
    deptId.value = id;
    deptFormModalRef.value!.formDialogModel = true;
  }
  async function getSystemDeptTree() {
    try {
      deptLoading.value = true;
      const { data } = await getSystemDeptTreeApi();

      deptOptions.value = data;

      // 设置部门id
      if (!branchId.value) {
        branchId.value = data[0].id;
      }

      reloadTable();
    } finally {
      deptLoading.value = false;
    }
  }
  // 获取角色
  async function getRoleList() {
    const { data } = await get_admin_role_list();
    roleOptions.value = data;
  }

  onMounted(() => {
    getSystemDeptTree();
    getRoleList();
  });
</script>

<style lang="less" scoped>
  .user-management {
    :deep(.n-tree-node-content) {
      flex: 1;
    }
  }
</style>
