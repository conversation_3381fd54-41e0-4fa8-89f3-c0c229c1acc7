<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable(), resetDateQuery()"
    >
      <template #blongUserId="{ model, field }">
        <NSelect
          v-model:value="model[field]"
          label-field="username"
          value-field="id"
          multiple
          :options="userList"
          placeholder="请选择归属人"
          filterable
        />
      </template>
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      :scroll-x="2200"
      ref="action"
      :striped="true"
    >
      <template #tableTitle>
        <n-button v-permission="{ action: 'add' }" type="primary" @click="handleOpenForm">
          <template #icon>
            <n-icon>
              <PlusOutlined />
            </n-icon>
          </template>
          新增线索
        </n-button>
      </template>
    </BasicTable>
  </n-card>

  <FormModal ref="formModal" @close="reloadTable" />

  <DetailDrawer ref="detailDrawer" clueType="publicClue" @close="reloadTable" />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { reactive, useTemplateRef, ref, onMounted, computed } from 'vue';
  import { PlusOutlined } from '@vicons/antd';
  import FormModal from './FormModal.vue';
  import DetailDrawer from '@/components/DetailDrawer/index.vue';
  import { getCustomerPoolListApi, exportCustomerPoolApi, getChannelListApi } from '@/api/client';
  import {
    MediaSourceOptions,
    FollowStatusOptions,
    ClueStatusOptions,
    ContactStatusOptions,
    IntentionDegreeOptions,
    IsAddWeChatOptions,
  } from '../enum';
  import { getUserListApi } from '@/api/global';
  import dayjs from 'dayjs';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { useUser } from '@/store/modules/user';
  import { handleDateClearStatus } from '@/views/dashboard/workplace/utils/date';

  const userStore = useUser();
  const detailDrawerRef = useTemplateRef<InstanceType<typeof DetailDrawer>>('detailDrawer');
  // 归属人list
  const userList = ref<any[]>([]);
  const exportLoading = ref(false);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const formModalRef = useTemplateRef<InstanceType<typeof FormModal>>('formModal');
  const channelList = ref<any[]>([]);
  const dateQuery = ref<any>({
    createTimeQuery: [
      dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    triageTimeQuery: [],
    lastFollowTimeQuery: [],
  });
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'createTimeQuery',
        component: 'NDatePicker',
        label: '创建时间',
        defaultValue: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'createTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.createTimeQuery = value;
          },
        },
        noHidden: true,
      },
      {
        field: 'triageTimeQuery',
        component: 'NDatePicker',
        label: '分发时间',
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'triageTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.triageTimeQuery = value;
          },
        },
      },
      {
        field: 'lastFollowTimeQuery',
        component: 'NDatePicker',
        label: '跟进时间',
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'lastFollowTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.lastFollowTimeQuery = value;
          },
        },
      },
      {
        field: 'name',
        component: 'NInput',
        label: '姓名',
        componentProps: {
          placeholder: '请输入姓名',
        },
      },
      {
        field: 'mobileNo',
        component: 'NInput',
        label: '电话',
        componentProps: {
          placeholder: '请输入电话号码',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { mobileNo } = getFieldsValue();
            const formattedValue = mobileNo.replace(/\D/g, '');
            if (mobileNo !== formattedValue) {
              setFieldsValue({ mobileNo: formattedValue });
            }
          },
        },
      },
      {
        field: 'blongUserIdIn',
        label: '归属人',
        slot: 'blongUserId',
      },
      {
        field: 'channelCode',
        component: 'NSelect',
        label: '线索来源',
        componentProps: {
          placeholder: '请选择线索来源',
          options: channelList.value,
        },
      },
      {
        field: 'mediaPlatformSourceIn',
        component: 'NSelect',
        label: '来源媒体',
        componentProps: {
          placeholder: '请选择来源媒体',
          options: MediaSourceOptions,
          multiple: true,
        },
      },
      {
        field: 'followStatusIn',
        component: 'NSelect',
        label: '跟进状态',
        componentProps: {
          placeholder: '请选择跟进状态',
          options: FollowStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'clueStatusIn',
        component: 'NSelect',
        label: '线索状态',
        componentProps: {
          placeholder: '请选择线索状态',
          options: ClueStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'communicationStatusIn',
        component: 'NSelect',
        label: '通讯状态',
        componentProps: {
          placeholder: '请选择通讯状态',
          options: ContactStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'intentIn',
        component: 'NSelect',
        label: '意向度',
        componentProps: {
          placeholder: '请选择意向度',
          options: IntentionDegreeOptions,
          multiple: true,
        },
      },
      {
        field: 'addWeChat',
        component: 'NSelect',
        label: '是否加微',
        componentProps: {
          placeholder: '请选择是否加微',
          options: IsAddWeChatOptions,
        },
      },
    ];
  });
  const [register, { getFieldsValue, setFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });
  const actionColumn = reactive({
    width: 80,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-button
          v-permission={{ action: 'detail' }}
          type="primary"
          text
          onClick={() => {
            detailDrawerRef.value?.openDrawer(record.id);
          }}
        >
          详情
        </n-button>
      );
    },
  });

  const loadDataTable = async (params) => {
    const { data } = await getCustomerPoolListApi({ ...getFieldsValue(), ...params });
    return data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  function handleOpenForm() {
    formModalRef.value!.formDialogModel = true;
  }
  function handleExport() {
    try {
      exportLoading.value = true;
      window.open(
        exportCustomerPoolApi({
          ...getFieldsValue(),
          pageNumber: 1,
          pageSize: 10000,
          token: userStore.getToken,
        })
      );
    } finally {
      exportLoading.value = false;
    }
  }
  async function getUserList() {
    try {
      const { data } = await getUserListApi();

      userList.value = data;
    } catch (err) {
      console.log(err);
    }
  }
  async function getChannelList() {
    try {
      const { data } = await getChannelListApi();

      channelList.value = data.map((item) => ({
        label: item.channelName,
        value: item.channelCode,
      }));
    } catch (err) {
      console.log(err);
    }
  }
  function resetDateQuery() {
    dateQuery.value = {
      createTimeQuery: [
        dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      triageTimeQuery: [],
      lastFollowTimeQuery: [],
    };
  }

  onMounted(() => {
    getUserList();
    getChannelList();
  });
</script>
