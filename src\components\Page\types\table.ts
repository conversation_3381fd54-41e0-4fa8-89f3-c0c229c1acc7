import type { DataTableProps, DataTableColumn, PaginationProps } from 'naive-ui/lib/data-table';
import { ComponentType } from './index';

// 表格列渲染类型
export type TableColumnRender = (row: any, index: number) => any;

// 表格操作按钮配置
export interface TableActionButton {
  label: string;
  type?: 'primary' | 'info' | 'success' | 'warning' | 'error';
  size?: 'tiny' | 'small' | 'medium' | 'large';
  ghost?: boolean;
  disabled?: boolean | ((row: any) => boolean);
  show?: boolean | ((row: any) => boolean);
  permission?: string;
  onClick: (row: any, index: number) => void;
  [key: string]: any;
}

// 表格列配置扩展
export interface TableColumnExtended extends Omit<DataTableColumn, 'key' | 'render'> {
  // 基础配置
  key: string;
  title: string;

  // 列类型
  component?: ComponentType;

  // 显示配置
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  fixed?: 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  ellipsis?: boolean | { tooltip?: boolean };

  // 排序筛选
  sortable?: boolean;
  filterable?: boolean;
  filterOptions?: Array<{ label: string; value: any }>;
  filterMultiple?: boolean;

  // 自定义渲染
  render?: TableColumnRender;
  slot?: string;

  // 特殊列类型配置
  actions?: TableActionButton[];
  tagOptions?: {
    type?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';
    size?: 'small' | 'medium' | 'large';
    round?: boolean;
    bordered?: boolean;
    mapping?: Record<string, { type?: string; text?: string }>;
  };

  // 其他配置
  [key: string]: any;
}

// 表格工具栏配置
export interface TableToolbar {
  title?: string;
  showRefresh?: boolean;
  showDensity?: boolean;
  showColumnSetting?: boolean;
  showFullscreen?: boolean;
  extra?: any[];
}

// 表格分页配置
export interface TablePagination extends PaginationProps {
  show?: boolean;
  position?: 'top' | 'bottom' | 'both';
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean | ((total: number, range: [number, number]) => string);
  pageSizes?: number[];
  [key: string]: any;
}

// 表格选择配置
export interface TableSelection {
  type?: 'checkbox' | 'radio';
  fixed?: boolean;
  width?: number;
  disabled?: (row: any) => boolean;
  onSelect?: (row: any, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  onSelectionChange?: (selectedRows: any[]) => void;
}

// 表格配置扩展
export interface TableConfigExtended extends Omit<DataTableProps, 'columns' | 'data'> {
  // 列配置
  columns: TableColumnExtended[];

  // 数据配置
  rowKey?: string | ((row: any) => string);

  // 分页配置
  pagination?: boolean | TablePagination;

  // 选择配置
  selection?: boolean | TableSelection;

  // 工具栏配置
  toolbar?: boolean | TableToolbar;

  // 显示配置
  showIndex?: boolean;
  indexColumn?: Partial<TableColumnExtended>;
  bordered?: boolean;
  striped?: boolean;
  size?: 'small' | 'medium' | 'large';

  // 加载配置
  loading?: boolean;
  empty?: string | (() => any);

  // 滚动配置
  scrollX?: number;
  scrollY?: number;
  virtualScroll?: boolean;

  // 事件回调
  onRowClick?: (row: any, index: number) => void;
  onRowDblclick?: (row: any, index: number) => void;
  onSorterChange?: (sorter: any) => void;
  onFiltersChange?: (filters: any) => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;

  // 其他配置
  [key: string]: any;
}

// 表格实例方法
export interface TableInstance {
  reload: (resetPage?: boolean) => Promise<void>;
  getSelectedRows: () => any[];
  clearSelection: () => void;
  setSelection: (rows: any[]) => void;
  scrollTo: (options: { left?: number; top?: number }) => void;
}

// 表格事件
export interface TableEvent {
  type:
    | 'rowClick'
    | 'rowDblclick'
    | 'selectionChange'
    | 'sorterChange'
    | 'filtersChange'
    | 'pageChange';
  data?: any;
  row?: any;
  index?: number;
  selectedRows?: any[];
  sorter?: any;
  filters?: any;
  page?: number;
  pageSize?: number;
}
