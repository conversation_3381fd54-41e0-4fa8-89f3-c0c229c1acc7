import type { DrawerProps } from 'naive-ui/lib/drawer';
import type { FormSchemaExtended } from './form';

// 弹窗模式
export type DialogMode = 'add' | 'edit' | 'view' | 'custom';

// 弹窗按钮配置
export interface DialogButton {
  label: string;
  type?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';
  size?: 'tiny' | 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  ghost?: boolean;
  onClick?: (formData?: any) => void | Promise<void>;
  [key: string]: any;
}

// 弹窗配置扩展
export interface DialogConfigExtended extends Omit<DrawerProps, 'show'> {
  // 基础配置
  title?: string | ((mode: DialogMode, row?: any) => string);
  width?: number | string;
  height?: number | string;

  // 表单配置
  schemas: FormSchemaExtended[];

  // 显示配置
  maskClosable?: boolean;
  closeOnEsc?: boolean;
  showClose?: boolean;

  // 按钮配置
  showFooter?: boolean;
  footer?: DialogButton[];
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonProps?: Partial<DialogButton>;
  cancelButtonProps?: Partial<DialogButton>;

  // 表单配置
  labelWidth?: number | string;
  labelPlacement?: 'left' | 'top';

  // 事件回调
  onOpen?: (mode: DialogMode, row?: any) => void;
  onClose?: () => void;
  onConfirm?: (formData: any, mode: DialogMode) => void | Promise<void>;
  onCancel?: () => void;

  // 数据处理
  beforeOpen?: (mode: DialogMode, row?: any) => any | Promise<any>;
  beforeClose?: (formData?: any) => boolean | Promise<boolean>;

  // 验证配置
  validateOnClose?: boolean;

  // 其他配置
  [key: string]: any;
}

// 弹窗实例方法
export interface DialogInstance {
  open: (mode: DialogMode, row?: any) => void;
  close: () => void;
  confirm: () => Promise<void>;
  cancel: () => void;
  getFormData: () => Record<string, any>;
  setFormData: (data: Record<string, any>) => void;
  resetForm: () => void;
  validateForm: () => Promise<any>;
}

// 弹窗事件
export interface DialogEvent {
  type: 'open' | 'close' | 'confirm' | 'cancel';
  mode?: DialogMode;
  data?: any;
  row?: any;
}

// 弹窗状态
export interface DialogState {
  visible: boolean;
  mode: DialogMode;
  title: string;
  loading: boolean;
  formData: Record<string, any>;
  currentRow: any;
}
