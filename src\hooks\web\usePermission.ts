import { useUserStore } from '@/store/modules/user';
import { asyncRoutes } from '@/router/index';
import { cloneDeep } from 'lodash-es';
/**
 * 平铺路由
 * @param tree
 * @returns
 */
function flattenTree(tree) {
  const result: any[] = [];
  function traverse(node, parentPath?: string) {
    if (Array.isArray(node)) {
      for (const item of node) {
        // 删除 children 字段，避免冗余数据
        const { children, ...rest } = item;
        rest.path = (parentPath ? parentPath + '/' : '') + rest.path;
        result.push(rest);
        if (children && children.length > 0) {
          traverse(children, rest.path);
        }
      }
    } else if (node && node.path) {
      result.push({ node, path: parentPath ? parentPath + '/' : '' + node.path });
    }
  }

  traverse(tree);
  return result;
}
//平铺后的路由
export const constantRoutes = flattenTree(cloneDeep(asyncRoutes));
export function usePermission() {
  const userStore = useUserStore();

  /**
   * 检查权限
   * @param accesses
   */
  function _somePermissions(accesses: string[]) {
    return userStore.getPermissions.some((item) => {
      const { value }: any = item;
      return accesses.includes(value);
    });
  }

  /**
   * 判断是否存在权限
   * 可用于 v-if 显示逻辑
   * */
  function hasPermission(accesses: string[]): boolean {
    if (!accesses || !accesses.length) return true;
    return _somePermissions(accesses);
  }

  /**
   * 是否包含指定的所有权限
   * @param accesses
   */
  function hasEveryPermission(accesses: string[]): boolean {
    const permissionsList = userStore.getPermissions;
    if (Array.isArray(accesses)) {
      return permissionsList.every((access: any) => accesses.includes(access.value));
    }
    throw new Error(`[hasEveryPermission]: ${accesses} should be a array !`);
  }

  /**
   * 是否包含其中某个权限
   * @param accesses
   * @param accessMap
   */
  function hasSomePermission(accesses: string[]): boolean {
    const permissionsList = userStore.getPermissions;
    if (Array.isArray(accesses)) {
      return permissionsList.some((access: any) => accesses.includes(access.value));
    }
    throw new Error(`[hasSomePermission]: ${accesses} should be a array !`);
  }

  async function rolesButtons() {
    let buttons = userStore.getButtons;
    if (!buttons || !buttons.length) {
      await userStore.getInfo();
      buttons = userStore.getButtons;
    }
    return buttons;
  }
  async function buttonsHasPermission(code: string) {
    const buttons = await rolesButtons();
    return buttons?.includes(code);
  }

  /**
   * 处理角色菜单遍历数据
   * @param {*} menuData 来自接口数据
   */

  function handleMenuData(menuData) {
    if (!menuData) return [];

    const findCurPath = (item, cur) => {
      if (item.path === cur.path && item.meta.buttons) {
        const cc = cloneDeep(cur.children);
        const { buttonsPreFix } = item.meta;
        const pushChild = item.meta?.buttons?.map((el) => {
          return { id: buttonsPreFix ? buttonsPreFix + el.key : el.key, title: el.name };
        });
        pushChild && pushChild.unshift({ id: cur.path + '_' + 'review', title: '查看' });
        cur.children = cc ? [...pushChild, ...cc] : pushChild;
        return cur;
      }
    };
    const handleChildren = (cur) => {
      const handleFindCurPath = () => {
        for (let i = 0; i < constantRoutes.length; i++) {
          const item = constantRoutes[i];
          const insertCur = findCurPath(item, cur);
          if (insertCur) {
            cur = { ...insertCur };
            break;
          }
        }
      };
      handleFindCurPath();
      if (cur && cur.children && cur.children.length > 0) {
        cur.children.forEach((item) => {
          handleChildren(item);
        });
      }
      return cur;
    };

    return menuData.reduce((acc, cur) => {
      const arrayList = handleChildren(cur);
      arrayList && acc.push(arrayList);
      return acc;
    }, []);
  }
  return {
    hasPermission,
    hasEveryPermission,
    hasSomePermission,
    rolesButtons,
    buttonsHasPermission,
    handleMenuData,
  };
}
