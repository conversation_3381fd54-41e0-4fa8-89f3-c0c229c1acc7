/**
 * 简化版本检查工具
 * 通过对比index.html中的meta标签版本号来检测应用更新
 */

import { showVersionUpdateDialog, showSimpleVersionUpdateDialog } from './versionUpdateDialog';

interface VersionCheckOptions {
  /** 检查间隔时间（毫秒），默认30秒 */
  interval?: number;
  /** 版本更新回调函数 */
  onVersionUpdate?: (newVersion: string, currentVersion: string) => Promise<string>;
}

class VersionChecker {
  private currentVersion = '';
  private timer: number | null = null;
  private options: Required<VersionCheckOptions>;
  private isChecking = false;

  constructor(options: VersionCheckOptions = {}) {
    this.options = {
      interval: 30000, // 默认30秒检查一次
      onVersionUpdate: this.showUpdateDialog,
      ...options,
    };

    this.init();
  }

  /**
   * 初始化版本检查器
   */
  private init(): void {
    this.currentVersion = this.getCurrentVersion();
    if (!this.currentVersion) {
      console.warn('无法获取当前版本号，版本检查功能将不可用');
      return;
    }
    this.setupFocusListener();
  }

  /**
   * 获取当前版本号（从meta标签中读取）
   */
  private getCurrentVersion(): string {
    const metaElement = document.querySelector('meta[name="app-version"]') as HTMLMetaElement;
    return metaElement?.content || '';
  }

  /**
   * 启动定时检查
   */
  private startPeriodicCheck(): void {
    if (this.timer) {
      clearInterval(this.timer);
    }

    this.timer = window.setInterval(() => {
      this.checkVersion();
    }, this.options.interval);
  }

  /**
   * 设置页面焦点监听器
   */
  private setupFocusListener(): void {
    // 监听页面可见性变化
    if (document.visibilityState === 'visible') {
      this.startPeriodicCheck();
    }
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('页面隐藏，停止版本检查');
        this.stop();
      } else {
        console.log('页面显示，开始版本检查');
        // 页面重新获得焦点时检查版本
        this.restart();
      }
    });
  }

  /**
   * 检查版本更新
   */
  private async checkVersion(): Promise<void> {
    if (this.isChecking) {
      return;
    }
    this.isChecking = true;

    try {
      // 请求index.html获取最新版本号
      const response = await fetch(
        `${window.location.origin}${window.location.pathname}?t=${Date.now()}`,
        {
          method: 'GET',
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      const newVersion = this.extractVersionFromHtml(html);
      if (newVersion && newVersion !== this.currentVersion) {
        console.log('检测到新版本:', newVersion, '当前版本:', this.currentVersion);
        await this.options.onVersionUpdate(newVersion, this.currentVersion);
      }
    } catch (error) {
      console.warn('版本检查失败:', error);
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * 从HTML字符串中提取版本号
   */
  private extractVersionFromHtml(html: string): string {
    const match = html.match(/<meta\s+name=["']app-version["']\s+content=["']([^"']+)["']/i);
    return match ? match[1] : '';
  }

  /**
   * 默认的版本更新对话框
   */
  private showUpdateDialog(newVersion: string, currentVersion: string): Promise<string> {
    try {
      // 尝试使用 Naive UI 弹窗
      return showVersionUpdateDialog(newVersion, currentVersion);
    } catch (error) {
      // 如果 Naive UI 不可用，使用简单的确认框
      console.warn('Naive UI 弹窗不可用，使用简单确认框:', error);
      return showSimpleVersionUpdateDialog(newVersion, currentVersion);
    }
  }

  /**
   * 手动检查版本
   */
  public checkNow(): Promise<void> {
    return this.checkVersion();
  }

  /**
   * 停止版本检查
   */
  public stop(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.isChecking = false;
  }

  /**
   * 重新启动版本检查
   */
  public restart(): void {
    this.stop();
    this.startPeriodicCheck();
  }

  /**
   * 更新检查选项
   */
  public updateOptions(options: Partial<VersionCheckOptions>): void {
    this.options = { ...this.options, ...options };
    this.restart();
  }
}

// 创建全局版本检查器实例
let versionChecker: VersionChecker | null = null;

/**
 * 初始化版本检查器
 */
export function initVersionChecker(options?: VersionCheckOptions): VersionChecker {
  if (versionChecker) {
    versionChecker.stop();
  }

  versionChecker = new VersionChecker(options);
  return versionChecker;
}

/**
 * 获取版本检查器实例
 */
export function getVersionChecker(): VersionChecker | null {
  return versionChecker;
}

/**
 * 停止版本检查
 */
export function stopVersionChecker(): void {
  if (versionChecker) {
    versionChecker.stop();
    versionChecker = null;
  }
}

export default VersionChecker;
