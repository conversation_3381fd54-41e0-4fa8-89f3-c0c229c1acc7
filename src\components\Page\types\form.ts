import type { FormProps, FormItemProps } from 'naive-ui/lib/form';
import type { GridProps, GridItemProps } from 'naive-ui/lib/grid';
import { ComponentType } from './index';

// 表单验证规则
export interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  type?:
    | 'string'
    | 'number'
    | 'boolean'
    | 'method'
    | 'regexp'
    | 'integer'
    | 'float'
    | 'array'
    | 'object'
    | 'enum'
    | 'date'
    | 'url'
    | 'hex'
    | 'email';
  pattern?: RegExp;
  min?: number;
  max?: number;
  len?: number;
  validator?: (rule: any, value: any) => boolean | Error | Promise<void>;
  [key: string]: any;
}

// 表单项配置扩展
export interface FormSchemaExtended extends Omit<FormItemProps, 'path'> {
  // 基础配置
  field: string;
  label: string;
  component: ComponentType;

  // 组件配置
  componentOptions?: {
    // 通用属性
    placeholder?: string;
    disabled?: boolean;
    clearable?: boolean;
    size?: 'small' | 'medium' | 'large';

    // NSelect 特有
    options?: Array<{ label: string; value: any; disabled?: boolean }>;
    multiple?: boolean;
    filterable?: boolean;
    remote?: boolean;
    loading?: boolean;
    onSearch?: (query: string) => void;

    // NDatePicker 特有
    type?: 'date' | 'datetime' | 'daterange' | 'datetimerange' | 'month' | 'year';
    format?: string;
    valueFormat?: string;

    // NInputNumber 特有
    min?: number;
    max?: number;
    step?: number;
    precision?: number;

    // NUpload 特有
    action?: string;
    headers?: Record<string, string>;
    accept?: string;
    multiple?: boolean;
    maxCount?: number;
    fileList?: any[];

    // 其他组件特有属性
    [key: string]: any;
  };

  // 验证规则
  required?: boolean;
  rules?: FormRule[];

  // 显示控制
  hidden?: boolean;
  show?: boolean | ((formData: Record<string, any>) => boolean);

  // 布局配置
  span?: number;
  offset?: number;

  // 联动配置
  dependencies?: string[];

  // 自定义渲染
  render?: (formData: Record<string, any>) => any;
  slot?: string;

  // 其他配置
  [key: string]: any;
}

// 表单配置
export interface FormConfig extends Omit<FormProps, 'model'> {
  // 表单项配置
  schemas: FormSchemaExtended[];

  // 布局配置
  inline?: boolean;
  labelWidth?: number | string;
  labelPlacement?: 'left' | 'top';

  // 栅格配置
  gridProps?: GridProps;
  giProps?: GridItemProps;

  // 按钮配置
  showActionButtonGroup?: boolean;
  showResetButton?: boolean;
  showSubmitButton?: boolean;
  submitButtonText?: string;
  resetButtonText?: string;

  // 事件回调
  onSubmit?: (formData: Record<string, any>) => void | Promise<void>;
  onReset?: () => void | Promise<void>;
  onValuesChange?: (changedValues: Record<string, any>, allValues: Record<string, any>) => void;

  // 其他配置
  [key: string]: any;
}

// 表单实例方法
export interface FormInstance {
  validate: () => Promise<any>;
  restoreValidation: () => void;
  getFieldsValue: () => Record<string, any>;
  setFieldsValue: (values: Record<string, any>) => void;
  resetFields: () => void;
  clearValidate: (name?: string | string[]) => void;
}

// 表单事件
export interface FormEvent {
  type: 'submit' | 'reset' | 'change' | 'validate';
  data?: any;
  field?: string;
  value?: any;
}
