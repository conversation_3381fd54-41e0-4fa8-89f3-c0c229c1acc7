<template>
  <FormDialog
    v-model="formDialogModel"
    title="编辑参数"
    :form-model="paramForm"
    ref="formDialog"
    :rules="rules"
    @submit="handleSubmit"
    @close="handleClose"
    @open="handleOpen"
  >
    <n-form-item label="参数名称：" path="paramName">
      <n-input v-model:value="paramForm.paramName" placeholder="请输入参数名称" clearable />
    </n-form-item>
    <n-form-item label="参数说明：" path="paramDesc">
      <n-input
        v-model:value="paramForm.paramDesc"
        type="textarea"
        clearable
        placeholder="请输入参数说明"
      />
    </n-form-item>
    <n-form-item label="类型配置：" path="paramValue">
      <n-input v-model:value="paramForm.paramValue" clearable placeholder="请输入类型配置" />
    </n-form-item>
    <n-form-item label="状态：" path="status">
      <n-radio-group v-model:value="paramForm.status">
        <n-space>
          <n-radio v-for="item in FormStatusList" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-space>
      </n-radio-group>
    </n-form-item>
  </FormDialog>
</template>

<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { reactive, ref, useTemplateRef, nextTick } from 'vue';
  import { FormStatusList } from '@/enums';
  import rules from './rules';
  import { saveSystemConfigApi } from '@/api/system';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => null,
    },
  });

  const formDialogRef = useTemplateRef<InstanceType<typeof FormDialog>>('formDialog');
  const paramFormState = {
    paramName: '',
    paramDesc: '',
    paramValue: '',
    status: null,
  };
  const paramForm = reactive({ ...paramFormState });
  const formDialogModel = ref(false);

  const emits = defineEmits(['close']);
  const handleSubmit = async (params, done) => {
    try {
      await saveSystemConfigApi(params);
      window.$message.success('保存成功');
      formDialogModel.value = false;
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(paramForm, paramFormState);
    emits('close');
  };
  const handleOpen = () => {
    props.formData.id &&
      nextTick(() => {
        formDialogRef.value?.loadStaticData(props.formData);
      });
  };

  defineExpose({
    formDialogModel,
  });
</script>
