import { ref, watch, unref, nextTick } from 'vue';
import type { Ref, ComputedRef } from 'vue';
import type { FormSchema } from '../types/form';
import { storage } from '@/utils/Storage';
import { isObject, isNullOrUnDef } from '@/utils/is';

interface UseFormCacheContext {
  formModel: Recordable;
  getSchema: ComputedRef<FormSchema[]>;
  cacheKey: string;
  enableCache?: boolean;
  cacheTimeout?: number; // 缓存过期时间（秒）
  gridCollapsed?: Ref<boolean>; // 展开收起状态
}

/**
 * 计算到当天24点的秒数
 */
function getSecondsUntilMidnight(): number {
  const now = new Date();
  const midnight = new Date(now);
  midnight.setHours(24, 0, 0, 0); // 设置到明天0点
  return Math.floor((midnight.getTime() - now.getTime()) / 1000);
}

export function useFormCache({
  formModel,
  getSchema,
  cacheKey,
  enableCache = true,
  cacheTimeout, // 不设置默认值，在函数内部计算
  gridCollapsed,
}: UseFormCacheContext) {
  const isRestoring = ref(false);
  const cacheStorageKey = `form_cache_${cacheKey}`;
  const collapsedStorageKey = `form_collapsed_${cacheKey}`;

  // 如果没有指定缓存时间，则默认缓存到当天24点
  const actualCacheTimeout = cacheTimeout || getSecondsUntilMidnight();

  /**
   * 保存表单数据到缓存
   */
  function saveFormCache() {
    if (!enableCache || isRestoring.value) return;

    const schemas = unref(getSchema);
    if (!schemas || schemas.length === 0) return;

    const cacheData: Recordable = {};

    // 只缓存有值的字段
    schemas.forEach((schema) => {
      const { field } = schema;
      const value = formModel[field];

      if (!isNullOrUnDef(value) && value !== '' && value !== null) {
        cacheData[field] = value;
      }
    });

    // 如果有数据才保存
    if (Object.keys(cacheData).length > 0) {
      storage.set(cacheStorageKey, cacheData, actualCacheTimeout);
    }

    // 保存展开收起状态
    if (gridCollapsed) {
      storage.set(collapsedStorageKey, gridCollapsed.value, actualCacheTimeout);
    }
  }

  /**
   * 从缓存恢复表单数据
   */
  function restoreFormCache() {
    if (!enableCache) return false;

    const cachedData = storage.get(cacheStorageKey);
    const cachedCollapsed = storage.get(collapsedStorageKey);

    let hasData = false;

    isRestoring.value = true;

    try {
      // 恢复表单数据
      if (cachedData && isObject(cachedData)) {
        const schemas = unref(getSchema);
        const validFields = schemas.map((schema) => schema.field);

        // 只恢复有效字段的数据
        Object.keys(cachedData).forEach((field) => {
          if (validFields.includes(field)) {
            formModel[field] = cachedData[field];
          }
        });
        hasData = true;
      }

      // 恢复展开收起状态
      if (gridCollapsed && cachedCollapsed !== null && cachedCollapsed !== undefined) {
        gridCollapsed.value = cachedCollapsed;
        hasData = true;
      }

      nextTick(() => {
        isRestoring.value = false;
      });

      return hasData;
    } catch (error) {
      console.warn('恢复表单缓存失败:', error);
      isRestoring.value = false;
      return false;
    }
  }

  /**
   * 清除表单缓存
   */
  function clearFormCache() {
    storage.remove(cacheStorageKey);
    storage.remove(collapsedStorageKey);
  }

  /**
   * 检查是否有缓存数据
   */
  function hasCacheData(): boolean {
    if (!enableCache) return false;
    const cachedData = storage.get(cacheStorageKey);
    const cachedCollapsed = storage.get(collapsedStorageKey);

    const hasFormData = cachedData && isObject(cachedData) && Object.keys(cachedData).length > 0;
    const hasCollapsedData = cachedCollapsed !== null && cachedCollapsed !== undefined;

    return hasFormData || hasCollapsedData;
  }

  /**
   * 监听表单数据变化，自动保存缓存
   */
  function watchFormChanges() {
    if (!enableCache) return;

    // 使用深度监听表单模型变化
    watch(
      () => formModel,
      () => {
        saveFormCache();
      },
      {
        deep: true,
        flush: 'post', // 在DOM更新后执行
      }
    );

    // 监听展开收起状态变化
    if (gridCollapsed) {
      watch(
        gridCollapsed,
        () => {
          saveFormCache();
        },
        {
          flush: 'post',
        }
      );
    }
  }

  return {
    saveFormCache,
    restoreFormCache,
    clearFormCache,
    hasCacheData,
    watchFormChanges,
    isRestoring,
  };
}
