// 导出所有类型
export * from './form';
export * from './table';
export * from './dialog';

// 组件类型枚举
export enum ComponentType {
  // 输入类
  NInput = 'NInput',
  NInputNumber = 'NInputNumber',
  NTextarea = 'NTextarea',
  NAutoComplete = 'NAutoComplete',

  // 选择类
  NSelect = 'NSelect',
  NCascader = 'NCascader',
  NTreeSelect = 'NTreeSelect',
  NTransfer = 'NTransfer',

  // 日期时间类
  NDatePicker = 'NDatePicker',
  NTimePicker = 'NTimePicker',

  // 选择器类
  NCheckbox = 'NCheckbox',
  NCheckboxGroup = 'NCheckboxGroup',
  NRadio = 'NRadio',
  NRadioGroup = 'NRadioGroup',
  NSwitch = 'NSwitch',
  NRate = 'NRate',
  NSlider = 'NSlider',

  // 上传类
  NUpload = 'NUpload',

  // 自定义类
  NColorPicker = 'NColorPicker',
  NMention = 'NMention',

  // 表格列类型
  Tag = 'tag',
  Actions = 'actions',
  Custom = 'custom',
}

// 请求配置
export interface RequestConfig {
  get?: (params?: any) => Promise<any>;
  add?: (data: any) => Promise<any>;
  edit?: (data: any) => Promise<any>;
  del?: (id: any) => Promise<any>;
  [key: string]: any;
}

// 页面配置主接口
export interface PageConfig {
  filter?: import('./form').FormConfig;
  table: import('./table').TableConfigExtended;
  dialog?: import('./dialog').DialogConfigExtended;
  request: RequestConfig;
}

// 页面操作类型
export enum PageActionType {
  Add = 'add',
  Edit = 'edit',
  Delete = 'delete',
  View = 'view',
  Search = 'search',
  Reset = 'reset',
  Refresh = 'refresh',
}

// 页面事件
export interface PageEvent {
  type: PageActionType;
  data?: any;
  row?: any;
  index?: number;
}

// 页面状态
export interface PageState {
  loading: boolean;
  tableData: any[];
  total: number;
  currentPage: number;
  pageSize: number;
  filterData: Record<string, any>;
  dialogVisible: boolean;
  dialogMode: 'add' | 'edit' | 'view';
  currentRow: any;
}
