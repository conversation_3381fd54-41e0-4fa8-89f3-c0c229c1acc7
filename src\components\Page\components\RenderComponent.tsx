export default {
  name: 'RenderComponent',
  props: {
    value: null,
    item: Object,
    row: Object,
    column: Object,
    index: Number,
  },
  render(h) {
    const { renderFunc, ...props } = this.$props;

    // 检查renderFunc是否为普通函数（非箭头函数）
    if (typeof renderFunc !== 'function') {
      throw new Error('renderFunc must be a function');
    }

    // 检查是否为箭头函数（箭头函数没有prototype属性）
    if (!renderFunc.prototype) {
      return renderFunc(h, props, this);
    }

    // 将当前组件实例作为context，让render函数可以访问完整的this
    return renderFunc.call(this, h, props);
  },
};
