export default {
  name: 'RenderComponent',
  props: {
    value: null,
    item: Object,
    row: Object,
    column: Object,
    index: Number,
  },
  render(h) {
    const { renderFunc, ...props } = this.$props;

    // 检查renderFunc是否为普通函数（非箭头函数）
    if (typeof renderFunc !== 'function') {
      throw new Error('renderFunc must be a function');
    }

    // 检查是否为箭头函数（箭头函数没有prototype属性）
    if (!renderFunc.prototype) {
      return renderFunc(h, props, this);
    }

    const a = {
      filter: {
        title: 'xxx',
        ...,
        schemas: [
          { field: 'name', label: '姓名', component: 'NInput', required: true },
          { field: 'email', label: '邮箱', component: 'NInput', rules: ['email'] },
          { field: 'status', label: '状态', component: 'NSelect', options: [] },
        ],
      },
      table: {
        pagination: true,
        selection: true,
        ...,
        columns: [
          { key: 'name', title: '姓名', width: 120 },
          { key: 'email', title: '邮箱', width: 200 },
          { key: 'status', title: '状态', type: 'tag' },
          { key: 'actions', title: '操作', type: 'actions' },
        ],
      },
      drawer: {
        width: 600,
        title: '用户信息',
        ...,
        schemas: [
          { field: 'name', label: '姓名', component: 'NInput', required: true },
          { field: 'email', label: '邮箱', component: 'NInput', rules: ['email'] },
          { field: 'status', label: '状态', component: 'NSelect', options: [] },
        ],
      },
      request: {
        get: () => Promise.resolve(),
        add: () => Promise.resolve(),
        edit: () => Promise.resolve(),
        del: () => Promise.resolve(),
      },
    };

    // 将当前组件实例作为context，让render函数可以访问完整的this
    return renderFunc.call(this, h, props);
  },
};
