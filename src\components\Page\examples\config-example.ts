import type { PageConfig } from '../types'
import { ComponentType } from '../types'

// 用户管理页面配置示例
export const userPageConfig: PageConfig = {
  // 筛选表单配置
  filter: {
    title: '用户筛选',
    inline: true,
    labelPlacement: 'left',
    labelWidth: 80,
    showResetButton: true,
    showSubmitButton: true,
    submitButtonText: '搜索',
    resetButtonText: '重置',
    schemas: [
      {
        field: 'name',
        label: '姓名',
        component: ComponentType.NInput,
        componentOptions: {
          placeholder: '请输入姓名',
          clearable: true
        },
        span: 6
      },
      {
        field: 'email',
        label: '邮箱',
        component: ComponentType.NInput,
        componentOptions: {
          placeholder: '请输入邮箱',
          clearable: true
        },
        span: 6
      },
      {
        field: 'status',
        label: '状态',
        component: ComponentType.NSelect,
        componentOptions: {
          placeholder: '请选择状态',
          clearable: true,
          options: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
          ]
        },
        span: 6
      },
      {
        field: 'createTime',
        label: '创建时间',
        component: ComponentType.NDatePicker,
        componentOptions: {
          type: 'daterange',
          placeholder: ['开始日期', '结束日期'],
          clearable: true
        },
        span: 6
      }
    ]
  },

  // 表格配置
  table: {
    bordered: true,
    striped: true,
    size: 'medium',
    pagination: {
      show: true,
      pageSize: 20,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条数据`
    },
    selection: {
      type: 'checkbox'
    },
    showIndex: true,
    columns: [
      {
        key: 'name',
        title: '姓名',
        width: 120,
        ellipsis: { tooltip: true }
      },
      {
        key: 'email',
        title: '邮箱',
        width: 200,
        ellipsis: { tooltip: true }
      },
      {
        key: 'phone',
        title: '手机号',
        width: 140
      },
      {
        key: 'status',
        title: '状态',
        width: 100,
        component: ComponentType.Tag,
        tagOptions: {
          mapping: {
            1: { type: 'success', text: '启用' },
            0: { type: 'error', text: '禁用' }
          }
        }
      },
      {
        key: 'createTime',
        title: '创建时间',
        width: 180,
        sortable: true
      },
      {
        key: 'actions',
        title: '操作',
        width: 200,
        fixed: 'right',
        component: ComponentType.Actions,
        actions: [
          {
            label: '编辑',
            type: 'primary',
            size: 'small',
            onClick: (row: any) => {
              console.log('编辑用户:', row)
            }
          },
          {
            label: '删除',
            type: 'error',
            size: 'small',
            onClick: (row: any) => {
              console.log('删除用户:', row)
            }
          }
        ]
      }
    ]
  },

  // 弹窗配置
  dialog: {
    title: '用户信息',
    width: 600,
    maskClosable: false,
    showFooter: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    labelWidth: 100,
    labelPlacement: 'left',
    schemas: [
      {
        field: 'name',
        label: '姓名',
        component: ComponentType.NInput,
        componentOptions: {
          placeholder: '请输入姓名'
        },
        required: true,
        rules: [
          { required: true, message: '请输入姓名' },
          { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符' }
        ]
      },
      {
        field: 'email',
        label: '邮箱',
        component: ComponentType.NInput,
        componentOptions: {
          placeholder: '请输入邮箱'
        },
        required: true,
        rules: [
          { required: true, message: '请输入邮箱' },
          { type: 'email', message: '请输入正确的邮箱格式' }
        ]
      },
      {
        field: 'phone',
        label: '手机号',
        component: ComponentType.NInput,
        componentOptions: {
          placeholder: '请输入手机号'
        },
        rules: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
        ]
      },
      {
        field: 'status',
        label: '状态',
        component: ComponentType.NRadioGroup,
        componentOptions: {
          options: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
          ]
        },
        required: true
      },
      {
        field: 'remark',
        label: '备注',
        component: ComponentType.NTextarea,
        componentOptions: {
          placeholder: '请输入备注',
          rows: 3,
          maxlength: 200,
          showCount: true
        }
      }
    ]
  },

  // 请求配置
  request: {
    get: async (params: any) => {
      // 模拟API请求
      console.log('获取用户列表:', params)
      return {
        data: [],
        total: 0
      }
    },
    add: async (data: any) => {
      console.log('新增用户:', data)
      return { success: true }
    },
    edit: async (data: any) => {
      console.log('编辑用户:', data)
      return { success: true }
    },
    del: async (id: any) => {
      console.log('删除用户:', id)
      return { success: true }
    }
  }
}
