<template>
  <div class="view-account">
    <div class="view-account-container">
      <div class="view-account-top flex flex-col">
        <div class="view-account-top-logo mx-auto">
          <img class="w-[258px] h-[45px]" src="@/assets/images/login/title.png" />
        </div>
      </div>
      <div class="view-account-form mt-[60px]">
        <n-form
          ref="formRef"
          label-placement="left"
          size="large"
          :model="formInline"
          :rules="rules"
        >
          <n-form-item path="username">
            <n-input
              class="input-style input-border-default"
              v-model:value="formInline.username"
              placeholder="请输入手机号"
              maxlength="11"
              @keyup.enter="handleSendCaptcha"
              :on-input="
                (value) => {
                  formInline.username = value.replace(/^0|[^0-9]/g, '');
                }
              "
            >
              <template #prefix>
                <n-icon size="18" color="#808695">
                  <PersonOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
          <n-form-item path="password">
            <n-input
              class="input-style input-border-default mt-[10px]"
              v-model:value="formInline.password"
              placeholder="请输入验证码"
              maxlength="4"
              @keyup.enter="handleSubmit"
              :on-input="
                (value) => {
                  formInline.password = value.replace(/^0|[^0-9]/g, '');
                }
              "
            >
              <template #prefix>
                <n-icon size="18" color="#808695">
                  <LockClosedOutline />
                </n-icon>
              </template>
              <template #suffix>
                <!-- 发送验证码按钮 -->
                <n-button
                  v-if="!formInline.isCaptcha"
                  class="sms-style"
                  type="primary"
                  size="small"
                  style="width: 100px"
                  :loading="captchaLoading"
                  @click="handleSendCaptcha"
                >
                  发送验证码
                </n-button>
                <!-- 倒计时 -->
                <n-button
                  v-else
                  class="sms-style"
                  type="primary"
                  size="small"
                  disabled
                  style="width: 100px"
                >
                  {{ formInline.countdown }}s
                </n-button>
              </template>
            </n-input>
          </n-form-item>
          <n-form-item class="default-color">
            <div class="flex justify-between">
              <div class="flex-initial">
                <n-checkbox v-model:checked="autoLogin">5天内自动登录</n-checkbox>
              </div>
            </div>
          </n-form-item>
          <n-form-item>
            <n-button
              class="button-style"
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="loading"
              block
            >
              登录
            </n-button>
          </n-form-item>
        </n-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { useMessage } from 'naive-ui';
  import { ResultEnum } from '@/enums/httpEnum';
  import { PersonOutline, LockClosedOutline } from '@vicons/ionicons5';
  import { PageEnum } from '@/enums/pageEnum';
  import { sendCaptcha } from '@/api/system/user';
  import type { ILoginParams } from '@/api/system/user';

  const formRef = ref();
  const message = useMessage();
  const loading = ref(false);
  const captchaLoading = ref(false);
  const autoLogin = ref(true);
  const LOGIN_NAME = PageEnum.BASE_LOGIN_NAME;
  const tokenCacheDays = 5;
  const formInline = reactive({
    username: '',
    password: '',
    isCaptcha: false,
    countdown: 59,
  });

  const rules = {
    username: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '请输入有效手机号', trigger: 'blur' },
    ],
    password: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { min: 4, max: 4, message: '验证码为4位数字', trigger: 'blur' },
    ],
  };

  const userStore = useUserStore();

  const router = useRouter();
  const route = useRoute();

  let timer: ReturnType<typeof setInterval> | null = null;

  const handleSendCaptcha = async () => {
    if (!formInline.username || !/^1[3456789]\d{9}$/.test(formInline.username)) {
      message.error('请输入有效手机号');
      return;
    }

    if (formInline.isCaptcha) return;

    try {
      captchaLoading.value = true;
      const params = {
        mobileNo: formInline.username,
      };
      await sendCaptcha(params);
      message.success('发送成功');

      captchaLoading.value = false;
      formInline.isCaptcha = true;
      formInline.countdown = 59;

      timer && clearInterval(timer);
      timer = setInterval(() => {
        formInline.countdown--;
        if (formInline.countdown <= 0) {
          formInline.isCaptcha = false;
          formInline.countdown = 59;
          timer && clearInterval(timer);
        }
      }, 1000);
    } catch (error) {
      captchaLoading.value = false;
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    formRef.value.validate(async (errors) => {
      if (errors) {
        message.error('请填写完整信息，并且进行验证码校验');
        return;
      }

      const { username, password } = formInline;
      message.loading('登录中...');
      loading.value = true;

      const params: ILoginParams = {
        mobileNo: username,
        smsCode: password,
        ...(autoLogin.value ? { needDelay: tokenCacheDays } : {}),
      };

      try {
        const { code } = await userStore.login(params);
        message.destroyAll();
        if (code == ResultEnum.SUCCESS) {
          const toPath = decodeURIComponent((route.query?.redirect || '/') as string);
          message.success('登录成功，即将进入系统');
          if (route.name === LOGIN_NAME) {
            await router.replace('/');
          } else await router.replace(toPath);
        }
      } catch (error: any) {
        message.destroyAll();
      } finally {
        loading.value = false;
      }
    });
  };
</script>

<style lang="less" scoped>
  .view-account {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100vh;
    overflow: auto;

    &-container {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0px 0px 9px 0px rgba(0, 43, 119, 0.1);
    }

    &-top {
      text-align: center;

      &-desc {
        font-size: 14px;
        color: #565a64;
      }
    }

    &-other {
      width: 100%;
    }

    .default-color {
      color: #515a6e;

      .ant-checkbox-wrapper {
        color: #515a6e;
      }
    }
  }

  @media (min-width: 1000px) {
    .view-account {
      background-image: url('../../assets/images/login/bg.png');
      background-repeat: no-repeat;
      background-size: cover;

      &-container {
        width: 1000px;
        height: 572px;
        background: #ffffff;
        box-shadow: 0px 0px 9px 0px rgba(0, 43, 119, 0.1);
        border-radius: 16px;
        background-image: url('../../assets/images/login/banner.png');
        background-repeat: no-repeat;
        background-size: 480px 572px;
        padding-left: 480px;
        max-width: 1000px;
        min-width: 320px;
        margin: 0 auto;
      }
    }
  }

  .input-style {
    width: 400px;
    height: 52px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #d0d2d9;
    box-shadow: none;

    // 移除 Naive UI 默认边框，避免重影
    :deep(.n-input__border),
    :deep(.n-input__state-border) {
      border: none !important;
    }

    // 移除默认的 box-shadow
    :deep(.n-input__border) {
      box-shadow: none !important;
    }

    // 自定义焦点状态
    &:focus-within {
      border-color: #1890ff;
    }

    :deep(.n-input__input-el) {
      height: 100%;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .button-style {
    width: 400px;
    height: 52px;
    background: #1677ff;
    box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
  }

  .sms-style {
    font-weight: 500;
    font-size: 16px;
    color: #999999;
    line-height: 22px;
    background: none;
    border: none;
    box-shadow: none;

    // 去除按钮边框
    :deep(.n-button__border) {
      border: none !important;
    }
  }
</style>
