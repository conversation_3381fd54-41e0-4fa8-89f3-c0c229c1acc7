import { get, post, put, del } from '@/utils/lib/axios.package';

export interface ListDate {
  label: string;
  key: string;
  type: number;
  subtitle: string;
  openType: number;
  auth: string;
  path: string;
  children?: ListDate[];
}

/**
 * @description: 根据用户id获取用户菜单
 */
export function adminMenus() {
  return get('/cms/menus');
}

/**
 * 获取tree菜单列表
 * @param params
 */
export function getMenuList(params?) {
  return get('/cms/admin/menu/list', params);
}
export const add_menu = (obj) => {
  return post('/cms/admin/menu', obj);
};

/**
 * 修改菜单
 */
export const put_menu = (obj) => {
  return put('/cms/admin/menu', obj);
};
/**
 * 删除菜单
 */
export const del_menu = (obj) => {
  return del(`/cms/admin/menu/${obj.id}`, obj);
};
