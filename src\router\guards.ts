import { PageEnum } from '@/enums/pageEnum';
import { ErrorPageRoute } from '@/router/base';
import { useAsyncRoute } from '@/store/modules/asyncRoute';
import { useUser } from '@/store/modules/user';
import { ACCESS_TOKEN } from '@/store/mutation-types';
import { storage } from '@/utils/Storage';
import type { RouteRecordRaw } from 'vue-router';
import { isNavigationFailure, Router } from 'vue-router';
import { RedirectName } from './constant';
import { permissionRouter } from '@/utils/index';
import { RootRoute } from './index';

const LOGIN_PATH = PageEnum.BASE_LOGIN;
const ERROR_NAME = PageEnum.ERROR_PAGE_NAME;
const ERROR_SON_NAME = PageEnum.ERROR_SON_PAGE_NAME;
const whitePathList = [
  LOGIN_PATH,
  ERROR_NAME,
  ERROR_SON_NAME,
  PageEnum.REDIRECT_NAME,
  PageEnum.REDIRECT_SON_NAME,
  PageEnum.ICON_PREVIEW,
]; // no redirect whitelist

export function createRouterGuards(router: Router) {
  const userStore = useUser();
  router.beforeEach(async (to, from, next) => {
    const Loading = window['$loading'] || null;
    Loading && Loading.start();

    // 防止从登录页跳转到错误页
    if (from.path === LOGIN_PATH && to.name === 'errorPage') {
      next(PageEnum.BASE_HOME);
      return;
    }

    // 白名单路由直接放行
    if (
      whitePathList.includes(to.path as PageEnum) ||
      whitePathList.includes(to.name as PageEnum)
    ) {
      next();
      return;
    }

    const token = storage.get(ACCESS_TOKEN);

    if (!token) {
      // 无权限访问的路由
      if (to.meta.ignoreAuth) {
        next();
        return;
      }
      // 重定向到登录页
      const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path: LOGIN_PATH,
        replace: true,
      };
      if (to.path && to.path !== LOGIN_PATH) {
        redirectData.query = {
          ...redirectData.query,
          redirect: to.path,
        };
      }
      next(redirectData);
      return;
    }
    try {
      const userInfo = await userStore.getInfo();
      const menus = userInfo?.menus || [];

      // 添加404错误页面
      const isErrorPage = router.getRoutes().findIndex((item) => item.name === ErrorPageRoute.name);
      if (isErrorPage === -1) {
        router.addRoute(ErrorPageRoute as unknown as RouteRecordRaw);
      }

      // 检查权限
      if ((menus.length === 0 || !permissionRouter(menus, to)) && to.path !== RootRoute.path) {
        next({ path: '/error/404' });
        return;
      }

      // 处理登录后的重定向逻辑，避免死循环
      if (from.query.redirect && from.path === LOGIN_PATH) {
        const redirectPath = decodeURIComponent(from.query.redirect as string);
        // 确保重定向路径有效且不会造成循环
        if (
          redirectPath &&
          redirectPath !== to.path &&
          redirectPath !== LOGIN_PATH &&
          redirectPath !== '/' &&
          !whitePathList.includes(redirectPath as PageEnum)
        ) {
          next({ path: redirectPath });
          return;
        }
      }

      next();
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 获取用户信息失败，清除token并重定向到登录页
      storage.remove(ACCESS_TOKEN);
      next({
        path: LOGIN_PATH,
        query: to.path !== LOGIN_PATH ? { redirect: to.path } : {},
        replace: true,
      });
      return;
    }

    Loading && Loading.finish();
  });

  router.afterEach((to, _, failure) => {
    document.title = (to?.meta?.title as string) || document.title;
    if (isNavigationFailure(failure)) {
      //console.log('failed navigation', failure)
    }
    const asyncRouteStore = useAsyncRoute();
    // 在这里设置需要缓存的组件名称
    const keepAliveComponents = asyncRouteStore.keepAliveComponents;
    const currentComName: any = to.matched.find((item) => item.name == to.name)?.name;
    if (currentComName && !keepAliveComponents.includes(currentComName) && to.meta?.keepAlive) {
      // 需要缓存的组件
      keepAliveComponents.push(currentComName);
    } else if (!to.meta?.keepAlive || to.name == RedirectName) {
      // 不需要缓存的组件
      const index = asyncRouteStore.keepAliveComponents.findIndex((name) => name == currentComName);
      if (index != -1) {
        keepAliveComponents.splice(index, 1);
      }
    }
    asyncRouteStore.setKeepAliveComponents(keepAliveComponents);
    const Loading = window['$loading'] || null;
    Loading && Loading.finish();
  });

  router.onError((error) => {
    console.log(error, '路由错误');
  });
}
