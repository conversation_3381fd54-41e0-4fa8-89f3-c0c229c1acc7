<template>
  <div class="sales-opportunity-container">
    <div class="table-actions">
      <n-button type="primary" @click="openSubmitModal">提交进件</n-button>
    </div>
    <n-data-table
      :columns="columns"
      :data="data"
      :pagination="{ pageSize: 15 }"
      :scroll-x="800"
      max-height="520px"
      :render-cell="renderCell"
    />
    <BaseModal v-model:show="showModal" title="提交进件确认" :on-confirm="handleConfirm">
      <div style="white-space: pre-wrap">
        请确认是否提交该线索至资方进件，提交成功后将自动创建贷款订单。订单未完结状态下不可提交其他进件。
      </div>
    </BaseModal>
  </div>
</template>

<script lang="ts" setup>
  import { h, ref } from 'vue';
  import { NDataTable, NButton, NTime } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import type { DataTableColumns } from 'naive-ui';
  import type { SalesOpportunity } from '@/api/detail';

  defineProps<{
    data: SalesOpportunity[];
  }>();

  const emit = defineEmits(['submit-success']);

  const showModal = ref(false);

  function openSubmitModal() {
    // showModal.value = true;
  }

  async function handleConfirm() {
    window.$message.success('提交成功');
    emit('submit-success');
  }

  const createColumns = (): DataTableColumns<SalesOpportunity> => [
    { title: '销售机会ID', key: 'saleOpportunityNo', width: 150, align: 'center' },
    { title: '可进件资方', key: 'managementCode', width: 120, align: 'center' },
    { title: '可进件产品', key: 'productName', width: 120, align: 'center' },
    { title: '产品利率', key: 'productRate', width: 100, align: 'center' },
    { title: '可贷金额(万)', key: 'loanAmount', width: 120, align: 'center' },
    {
      title: '匹配时间',
      key: 'matchTime',
      width: 180,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.matchTime) });
      },
    },
  ];

  const columns = createColumns();

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
</script>

<style scoped>
  .sales-opportunity-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
</style>
