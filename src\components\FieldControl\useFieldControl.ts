import { computed, watchEffect } from 'vue';
import { useStorage } from '@vueuse/core';
import type { FieldGroup, StoredGroupState } from '@/components/FieldControl/types';

/**
 * @description 管理字段显示、排序和持久化的 Composable Hook
 * @param storageKey - 用于本地存储的键名
 * @param initialConfigs - 初始字段分组配置
 */
export function useFieldControl(storageKey: string, initialConfigs: FieldGroup[]) {
  /**
   * @description: 根据初始配置生成默认状态
   */
  const getDefaultState = (): StoredGroupState[] => {
    return initialConfigs.map((group) => ({
      title: group.title,
      fields: group.configs.map((config) => ({
        key: config.key,
        label: config.label,
        visible: config.defaultVisible !== false,
      })),
    }));
  };

  const storedFieldGroups = useStorage<StoredGroupState[]>(
    storageKey,
    getDefaultState(),
    undefined,
    {
      deep: true,
    }
  );

  /**
   * @description: 从旧的、扁平化的数组结构自动迁移到新的分组结构
   * 通过检查数组第一项是否包含 'key' 且不包含 'fields' 来判断是否为旧格式
   */
  watchEffect(() => {
    const groups = storedFieldGroups.value;
    if (groups && Array.isArray(groups) && groups.length > 0 && 'key' in groups[0]) {
      console.warn('检测到旧版字段存储格式，将自动重置为新版分组格式。');
      storedFieldGroups.value = getDefaultState();
    }
  });

  /**
   * @description: 按分组计算出所有可见的字段
   * 返回一个以分组标题为键，可见字段数组为值的对象
   */
  const visibleFieldsByGroup = computed(() => {
    const result: Record<string, any[]> = {};
    const groups = storedFieldGroups.value;

    if (!Array.isArray(groups)) {
      return result;
    }

    for (const group of groups) {
      if (group && Array.isArray(group.fields)) {
        const visibleFields = group.fields.filter((field) => field.visible);
        // 保证输出的字段顺序与存储中的顺序一致
        const orderedKeys = group.fields.map((f) => f.key);
        result[group.title] = visibleFields.sort(
          (a, b) => orderedKeys.indexOf(a.key) - orderedKeys.indexOf(b.key)
        );
      }
    }
    return result;
  });

  /**
   * @description: 重置为默认字段配置
   */
  const resetFields = () => {
    storedFieldGroups.value = getDefaultState();
  };

  return {
    storedFieldGroups, // 暴露给 Modal 使用，用于渲染所有字段选项
    visibleFieldsByGroup, // 暴露给 Drawer 使用，用于渲染可见字段
    resetFields, // 暴露给 Modal 使用，用于重置
  };
}
