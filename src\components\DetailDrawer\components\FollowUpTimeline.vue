<template>
  <div class="timeline-container" ref="timelineRef">
    <n-timeline>
      <n-timeline-item
        v-for="item in records"
        :key="item.id"
        :type="getTagType(item.followUpType, item.communicationStatus)"
      >
        <template #header>
          <div class="timeline-header">
            <span>{{ item.createByName }}</span>
            <span class="timestamp">
              <n-time :time="new Date(item.createTime)" />
            </span>
            <n-tag size="small" :type="getTagType(item.followUpType, item.communicationStatus)">
              {{ getStatusLabel(item.followUpType, item.communicationStatus) }}
            </n-tag>
          </div>
        </template>
        <div class="timeline-content">
          <div>备注: {{ item.remark }}</div>
        </div>
      </n-timeline-item>
    </n-timeline>
  </div>
</template>

<script lang="ts" setup>
  import emitter from '@/utils/eventBus';
  import { useTemplateRef, onUnmounted } from 'vue';
  import { NTimeline, NTimelineItem, NTag, NTime } from 'naive-ui';
  import type { ClueFollowUpRecord } from '@/api/detail';
  import {
    followUpMethodEnum,
    phoneCommunicationStatusOptions,
    wechatCommunicationStatusOptions,
    communicationStatusEnum,
  } from '@/enums/detailEnum';

  defineProps<{
    records: ClueFollowUpRecord[];
  }>();

  const timelineRef = useTemplateRef<HTMLDivElement>('timelineRef');

  function getTagType(_type: followUpMethodEnum, status: number) {
    const successStatus = [communicationStatusEnum.CONNECTED, communicationStatusEnum.REPLIED];
    const errorStatus = [
      communicationStatusEnum.EMPTY_NUMBER,
      communicationStatusEnum.REJECTED,
      communicationStatusEnum.BLACKLISTED,
    ];

    if (successStatus.includes(status)) {
      return 'success';
    }
    if (errorStatus.includes(status)) {
      return 'error';
    }
    return 'info';
  }

  function getStatusLabel(type: followUpMethodEnum, status: number) {
    const methodLabel = type === followUpMethodEnum.PHONE ? '电话沟通' : '微信沟通';

    const options =
      type === followUpMethodEnum.PHONE
        ? phoneCommunicationStatusOptions
        : wechatCommunicationStatusOptions;

    const statusLabel = options.find((opt) => opt.value === status)?.label || '未知';

    return `${methodLabel}-${statusLabel}`;
  }

  function scrollToTop() {
    timelineRef.value?.scrollTo(0, 0);
  }

  emitter.on('add-follow-up-success', scrollToTop);

  onUnmounted(() => {
    emitter.off('add-follow-up-success', scrollToTop);
  });
</script>

<style lang="less" scoped>
  @import '@/styles/custom/scrollbar.less';

  .timeline-container {
    border: 1px solid #eee;
    max-height: 380px;
    overflow-y: auto;
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    .custom-scrollbar();
  }
  .timeline-header {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .timestamp {
    color: #999;
  }
  .timeline-content {
    color: #666;
    margin-top: 8px;
    font-size: 13px;
    line-height: 1.6;
  }
</style>
