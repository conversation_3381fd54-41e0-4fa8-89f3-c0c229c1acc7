<template>
  <div class="descriptions">
    <div class="header">{{ title }}</div>
    <div v-for="field in fields" :key="field.key" class="item">
      <div class="label">{{ field.label }}:</div>
      <div class="value" :style="getStyle(field)">{{ getDisplayValue(field) || '-' }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import type { PropType } from 'vue';
  import type { CustomerDetail } from '@/api/detail';
  import type { FieldConfig } from '@/components/FieldControl/types';
  import {
    sexOptions,
    followUpStatusOptions,
    educationLevelOptions,
    maritalStatusOptions,
    houseTypeOptions,
    companyNatureOptions,
    contactRelationOptions,
    monthlyIncomeOptions,
    vehicleStatusOptions,
    checkStatusOptions,
    // followUpStatusEnum,
  } from '@/enums/detailEnum';

  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    fields: {
      type: Array as PropType<FieldConfig[]>,
      required: true,
    },
    detail: {
      type: Object as PropType<CustomerDetail | null>,
      required: true,
    },
  });

  // 辅助函数，用于安全地访问嵌套属性
  function get(obj: any, path: string | string[]): any {
    const pathArray = Array.isArray(path) ? path : path.split('.').filter((i) => i.length);
    return pathArray.reduce((prev, curr) => prev?.[curr], obj);
  }

  // --- 枚举映射 ---
  const findLabel = (options, value) => options.find((o) => o.value === value)?.label;

  const getDisplayValue = (field: FieldConfig) => {
    if (!props.detail) return '-';

    switch (field.key) {
      case 'clueInfoVo.location': {
        const province = get(props.detail, 'clueInfoVo.provinceName');
        const city = get(props.detail, 'clueInfoVo.cityName');
        const locationString = [province, city]
          .filter((item) => item !== undefined && item !== null && item !== '')
          .join(' ');
        return locationString || '-';
      }
      case 'clueInfoVo.sex': {
        const value = get(props.detail, field.key);
        return findLabel(sexOptions, value);
      }
      case 'clueInfoVo.followStatus': {
        const value = get(props.detail, field.key);
        return findLabel(followUpStatusOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.childCount': {
        const value = get(props.detail, field.key);
        return value ? value : value === 0 ? '无子女' : '-';
      }
      case 'loanApplicationMaterials.personalInfo.educationLevel': {
        const value = get(props.detail, field.key);
        return findLabel(educationLevelOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.maritalStatus': {
        const value = get(props.detail, field.key);
        return findLabel(maritalStatusOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.houseType': {
        const value = get(props.detail, field.key);
        return findLabel(houseTypeOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.companyNature': {
        const value = get(props.detail, field.key);
        return findLabel(companyNatureOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.contactRelation': {
        const value = get(props.detail, field.key);
        return findLabel(contactRelationOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.monthlyIncome': {
        const value = get(props.detail, field.key);
        return findLabel(monthlyIncomeOptions, value);
      }
      case 'loanApplicationMaterials.personalInfo.threeElementsStatus': {
        const value = get(props.detail, field.key);
        return findLabel(checkStatusOptions, value);
      }
      case 'loanApplicationMaterials.vehicleInfo.vehicleStatus': {
        const value = get(props.detail, field.key);
        return findLabel(vehicleStatusOptions, value);
      }
      case 'loanApplicationMaterials.vehicleInfo.carOfPersonStatus': {
        const value = get(props.detail, field.key);
        return findLabel(checkStatusOptions, value);
      }
      case 'loanApplicationMaterials.vehicleInfo.factoryDate': {
        const value = get(props.detail, field.key);
        return value ? dayjs(value).format('YYYY-MM-DD') : '-';
      }
      default: {
        const value = get(props.detail, field.key);
        return value ?? '-';
      }
    }
  };

  const getStyle = (field: FieldConfig) => {
    const fontBoldField = ['clueInfoVo.followStatus', 'clueInfoVo.mobileNo', 'clueInfoVo.sex'];
    const style: Record<string, string> = {};

    if (fontBoldField.includes(field.key)) {
      style.fontWeight = 'bold';
    }

    if (field.key === 'clueInfoVo.followStatus') {
      // const value = get(props.detail, field.key);
      // style.color = value === followUpStatusEnum.FOLLOWING ? '#2080f0' : '';
      style.color = '#2080f0';
    }

    return style;
  };
</script>

<style lang="less" scoped>
  .descriptions {
    .header {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 16px;
      padding-left: 12px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        border-radius: 4px;
        background-color: #2080f0;
      }
    }
    .item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 18px;
      padding-left: 14px;
      font-size: 14px;
      gap: 16px;

      .label {
        color: #666;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .value {
        text-align: right;
        word-break: break-all;
      }
    }
  }
</style>
