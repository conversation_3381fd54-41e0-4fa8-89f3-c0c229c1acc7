import { createEnumOptions } from '@/utils/createEnumOptions';

// 线索来源枚举
export enum ClueSource {
  // 车贷业务
  CarLoan = 0,
  // 大联登媒体
  Media = 1,
}
export const ClueSourceMap = {
  [ClueSource.CarLoan]: '车贷业务',
  [ClueSource.Media]: '大联登媒体',
};
export const ClueSourceOptions = createEnumOptions(ClueSourceMap);

// 来源媒体枚举
export enum MediaSource {
  // 应用市场
  AppMarket = 0,
  // 信息流
  InformationFlow = 1,
  // 同业
  Industry = 2,
}
export const MediaSourceMap = {
  [MediaSource.AppMarket]: '应用市场',
  [MediaSource.InformationFlow]: '信息流',
  [MediaSource.Industry]: '同业',
};
export const MediaSourceOptions = createEnumOptions(MediaSourceMap);

// 跟进状态枚举
export enum FollowStatus {
  // 待跟进
  WaitFollow = 0,
  // 跟进中
  Following = 1,
  // 跟进结束
  FollowEnd = 2,
}
export const FollowStatusMap = {
  [FollowStatus.WaitFollow]: '待跟进',
  [FollowStatus.Following]: '跟进中',
  [FollowStatus.FollowEnd]: '跟进结束',
};
export const FollowStatusOptions = createEnumOptions(FollowStatusMap);

// 线索状态枚举
export enum ClueStatus {
  // 待首次跟进
  WaitFirstFollow = 0,
  // 待再次跟进
  WaitSecondFollow = 1,
  // 待转化
  WaitConvert = 2,
  // 已转化
  Converted = 3,
  // 已失效
  Invalid = 4,
}
export const ClueStatusMap = {
  [ClueStatus.WaitFirstFollow]: '待首次跟进',
  [ClueStatus.WaitSecondFollow]: '待再次跟进',
  [ClueStatus.Converted]: '已转化',
  [ClueStatus.WaitConvert]: '待转化',
  [ClueStatus.Invalid]: '已失效',
};
export const ClueStatusOptions = createEnumOptions(ClueStatusMap);

// 通讯状态枚举
export enum ContactStatus {
  // 已接通
  Connected = 1,
  // 空号
  EmptyNumber = 2,
  // 拒接
  Reject = 3,
  // 未拨通
  Unconnected = 4,
  // 已回复
  Replied = 5,
  // 发信息未回复
  SentMessageNotReplied = 6,
  // 拉黑/删除
  Blocked = 7,
}
export const ContactStatusMap = {
  [ContactStatus.Connected]: '已接通',
  [ContactStatus.EmptyNumber]: '空号',
  [ContactStatus.Reject]: '拒接',
  [ContactStatus.Unconnected]: '未拨通',
  [ContactStatus.Replied]: '已回复',
  [ContactStatus.SentMessageNotReplied]: '发信息未回复',
  [ContactStatus.Blocked]: '拉黑/删除',
};
export const ContactStatusOptions = createEnumOptions(ContactStatusMap);

// 意向度枚举
export enum IntentionDegree {
  // 无意向
  None = 1,
  // 低意向
  Low = 2,
  // 中意向
  Middle = 3,
  // 高意向
  High = 4,
}
export const IntentionDegreeMap = {
  [IntentionDegree.High]: '高意向',
  [IntentionDegree.Middle]: '中意向',
  [IntentionDegree.Low]: '低意向',
  [IntentionDegree.None]: '无意向',
};
export const IntentionDegreeOptions = createEnumOptions(IntentionDegreeMap);

// 是否加微枚举
export enum IsAddWeChat {
  // 否
  No = 0,
  // 是
  Yes = 1,
}
export const IsAddWeChatMap = {
  [IsAddWeChat.No]: '否',
  [IsAddWeChat.Yes]: '是',
};
export const IsAddWeChatOptions = createEnumOptions(IsAddWeChatMap);

// 线索跟进方枚举
export enum ClueFollower {
  // 自有CRM
  SelfCRM = 0,
  // 易顺CRM
  EasyCRM = 1,
}
export const ClueFollowerMap = {
  [ClueFollower.SelfCRM]: '自有CRM',
  [ClueFollower.EasyCRM]: '易顺CRM',
};
export const ClueFollowerOptions = createEnumOptions(ClueFollowerMap);

// 入库方式枚举
export enum ClueStoreType {
  // API接口
  API = 0,
  // 手动增加
  Manual = 1,
}
export const ClueStoreTypeMap = {
  [ClueStoreType.API]: 'API接口',
  [ClueStoreType.Manual]: '手动增加',
};
export const ClueStoreTypeOptions = createEnumOptions(ClueStoreTypeMap);
