<!--迁移弹窗-->
<template>
  <n-modal
    preset="dialog"
    title="账号数据移交"
    style="width: 95%"
    :show-icon="false"
    v-model:show="dialogVisible"
    :mask-closable="false"
    @after-leave="handleClose"
    @after-enter="handleOpen"
  >
    <n-card :bordered="false">
      <BasicForm
        :showSlotConfig="false"
        :enable-cache="true"
        cache-key="migrate-table"
        @register="register"
        @submit="reloadTable"
        @reset="reloadTable(), resetDateQuery()"
      />
    </n-card>
    <n-card :bordered="false" class="mt-3">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:ListData) => row.id"
        :actionColumn="actionColumn"
        :scroll-x="2200"
        ref="action"
        :striped="true"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheck"
      >
        <template #tableTitle>
          <div class="flex gap-[20px] items-end">
            <n-space>
              <n-button
                type="primary"
                :disabled="checkedRowKeys.length === 0"
                @click="handleClickTransfer(null)"
              >
                移交线索
              </n-button>
              <n-button
                type="primary"
                :disabled="checkedRowKeys.length === 0"
                @click="handleClickRelease(null)"
              >
                释放线索
              </n-button>
            </n-space>
            <div>请选择承接人进行账号移交，完成账号数据移交后，线索归属人将自动变更为继承人</div>
          </div>
        </template>
      </BasicTable>
    </n-card>
  </n-modal>

  <HandOverClues
    ref="handOverClues"
    :ids="curIds"
    :dept-tree="deptTree"
    :originalUserId="userId"
    @submit-success="reloadTableAndRestCheckRow"
  />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './migrateTableColumns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { reactive, useTemplateRef, ref, computed, watch } from 'vue';
  import {
    ClueStatusOptions,
    ContactStatusOptions,
    FollowStatusOptions,
    IntentionDegreeOptions,
    IsAddWeChatOptions,
    MediaSourceOptions,
  } from '@/views/client/enum';
  import HandOverClues from './HandOverClues.vue';
  import { getFollowUpListApi } from '@/api/dashboard/workplace';
  import dayjs from 'dayjs';
  import { useDialog } from 'naive-ui';
  import type { DataTableRowKey } from 'naive-ui';
  import { releaseClueApi } from '@/api/system';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { onMounted } from 'vue';
  import { getChannelListApi } from '@/api/client';
  import { handleDateClearStatus } from '@/views/dashboard/workplace/utils/date';

  const props = defineProps({
    deptTree: {
      type: Array,
      default: () => [],
    },
    userId: {
      type: Number,
      default: null,
    },
  });

  const dialog = useDialog();
  // 提交ids
  const curIds = ref<DataTableRowKey[]>([]);
  const checkedRowKeys = ref<DataTableRowKey[]>([]);
  const handOverCluesRef = useTemplateRef<InstanceType<typeof HandOverClues>>('handOverClues');
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const dialogVisible = ref(false);
  const channelList = ref<any[]>([]);
  const dateQuery = ref<any>({
    createTimeQuery: [
      dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    triageTimeQuery: [],
    lastFollowTimeQuery: [],
  });
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'createTimeQuery',
        component: 'NDatePicker',
        label: '创建时间',
        childKey: ['startTime', 'endTime'],
        defaultValue: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'createTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.createTimeQuery = value;
          },
        },
      },
      {
        field: 'triageTimeQuery',
        component: 'NDatePicker',
        label: '分发时间',
        childKey: ['triageStartTime', 'triageEndTime'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'triageTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.triageTimeQuery = value;
          },
        },
      },
      {
        field: 'lastFollowTimeQuery',
        component: 'NDatePicker',
        label: '跟进时间',
        childKey: ['lastFollowStartTime', 'lastFollowEndTime'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: handleDateClearStatus(dateQuery, 'lastFollowTimeQuery'),
          onUpdateValue: (value) => {
            dateQuery.value.lastFollowTimeQuery = value;
          },
        },
      },
      {
        field: 'name',
        component: 'NInput',
        label: '姓名',
        componentProps: {
          placeholder: '请输入姓名',
        },
      },
      {
        field: 'mobileNo',
        component: 'NInput',
        label: '电话',
        componentProps: {
          placeholder: '请输入电话号码',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { mobileNo } = getFieldsValue();
            const formattedValue = mobileNo.replace(/\D/g, '');
            if (mobileNo !== formattedValue) {
              setFieldsValue({ mobileNo: formattedValue });
            }
          },
        },
      },
      {
        field: 'channelCode',
        component: 'NSelect',
        label: '线索来源',
        componentProps: {
          placeholder: '请选择线索来源',
          options: channelList.value,
          multiple: true,
        },
      },
      {
        field: 'mediaPlatformSource',
        component: 'NSelect',
        label: '来源媒体',
        componentProps: {
          placeholder: '请选择来源媒体',
          options: MediaSourceOptions,
          multiple: true,
        },
      },
      {
        field: 'followStatus',
        component: 'NSelect',
        label: '跟进状态',
        componentProps: {
          placeholder: '请选择跟进状态',
          options: FollowStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'clueStatus',
        component: 'NSelect',
        label: '线索状态',
        componentProps: {
          placeholder: '请选择线索状态',
          options: ClueStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'communicationStatus',
        component: 'NSelect',
        label: '通讯状态',
        componentProps: {
          placeholder: '请选择通讯状态',
          options: ContactStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'intent',
        component: 'NSelect',
        label: '意向度',
        componentProps: {
          placeholder: '请选择意向度',
          options: IntentionDegreeOptions,
          multiple: true,
        },
      },
      {
        field: 'addWeChat',
        component: 'NSelect',
        label: '是否加微',
        componentProps: {
          placeholder: '请选择是否加微',
          options: IsAddWeChatOptions,
        },
      },
    ];
  });
  const [register, { getFieldsValue, setFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });
  const actionColumn = reactive({
    width: 80,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-space>
          <n-button
            type="primary"
            text
            onClick={() => {
              handleClickTransfer(record.id);
            }}
          >
            移交
          </n-button>
          <n-button
            type="primary"
            text
            onClick={() => {
              handleClickRelease(record.id);
            }}
          >
            释放
          </n-button>
        </n-space>
      );
    },
  });

  const loadDataTable = async (params) => {
    const { data } = await getFollowUpListApi({
      ...getFieldsValue(),
      ...params,
      blongUserId: props.userId,
    });
    return data;
  };
  const handleOpen = () => {};
  function reloadTable() {
    actionRef.value!.reload();
  }
  function handleClickTransfer(id) {
    if (id) {
      curIds.value = [id];
    } else {
      curIds.value = checkedRowKeys.value;
    }
    handOverCluesRef.value!.formDialogModel = true;
  }

  function handleClickRelease(id) {
    dialog.warning({
      title: '释放线索',
      content: '释放线索后，线索将流入客户公海，归属人置为空，同时保留线索跟进数据，请确认是否释放',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await releaseClueApi({
            releaseUserId: props.userId,
            ids: id ? [id] : checkedRowKeys.value,
          });

          window.$message.success('释放成功');
          reloadTableAndRestCheckRow();
        } catch (err) {
          console.log(err);
        }
      },
    });
  }
  function handleCheck(rowKeys: DataTableRowKey[]) {
    checkedRowKeys.value = rowKeys;
  }
  function handleClose() {
    checkedRowKeys.value = [];
  }
  async function getChannelList() {
    try {
      const { data } = await getChannelListApi();

      channelList.value = data.map((item) => ({
        label: item.channelName,
        value: item.channelCode,
      }));
    } catch (err) {
      console.log(err);
    }
  }
  function reloadTableAndRestCheckRow() {
    reloadTable();
    checkedRowKeys.value = [];
  }

  function resetDateQuery() {
    dateQuery.value = {
      createTimeQuery: [
        dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      triageTimeQuery: [],
      lastFollowTimeQuery: [],
    };
  }

  watch(
    () => dialogVisible.value,
    (value) => {
      if (!value) resetDateQuery();
    }
  );

  onMounted(() => {
    getChannelList();
  });

  defineExpose({
    dialogVisible,
  });
</script>
