import {
  intentionEnum,
  FollowUpNodeStatusEnum,
  followUpMethodEnum,
  communicationStatusEnum,
  clueStatusEnum,
  sexEnum,
  educationLevelEnum,
  maritalStatusEnum,
  houseTypeEnum,
  companyNatureEnum,
  contactRelationEnum,
  monthlyIncomeEnum,
  YesNoEnum,
  VehicleStatusEnum,
  CheckStatusEnum,
} from '@/enums/detailEnum';
import { get, post } from '@/utils/lib/axios.package';

/**
 * 获取线索详情
 * @param clueId 线索ID
 */
export function getClueDetail(clueId: number) {
  return get(`/cms/workspace/detail/${clueId}`);
}

/**
 * 获取线索详情-【公海】
 * @param clueId 线索ID
 */
export function getPublicClueDetail(clueId: number) {
  return get(`/cms/public-clue/detail/${clueId}`);
}

/**
 * 更新线索标签
 * @param clueId 线索ID
 * @param tags 标签数组
 */
export function updateClueTags(clueId: number, tags: string) {
  return post('/cms/workspace/add/tags', {
    clueId,
    tags,
  });
}

/**
 * 更新线索意向度
 * @param clueId 线索ID
 * @param intent 意向度
 */
export function updateClueIntention(clueId: number, intent: intentionEnum) {
  return post('/cms/workspace/update/intention', {
    clueId,
    intent,
  });
}

/**
 * 更新订单状态
 * @param params 请求参数
 */
export interface UpdateOrderStatusPayloadExt {
  signUrl?: string;
  productName?: string;
  creditLine?: string;
  productRate?: string;
  term?: string;
  loanAmount?: string;
  monthlyPayment?: string;
}

export interface UpdateOrderStatusParams {
  innerOrderNo: string;
  orderNode: string;
  nodeStatus: number | null;
  remark: string;
  ext: UpdateOrderStatusPayloadExt;
}

export function updateOrderStatus(params: UpdateOrderStatusParams) {
  return post('/cms/loan-order-record/update/order/orderNode', params);
}

/**
 * 添加跟进记录
 * @param params 请求参数
 */
export interface AddFollowUpRecordPayload {
  clueId: number;
  followUpType?: number;
  communicationStatus?: number | null;
  intent?: number | null;
  addWeChat?: number;
  remark?: string;
  addAgent?: number;
  type?: number;
  agentTime?: number | null;
  agentRemark?: string;
}

export function addFollowUpRecord(params: AddFollowUpRecordPayload) {
  return post('/cms/workspace/add/follow-up/record', params);
}

/**
 * 标记跟进状态
 * @param clueId 线索ID
 * @param clueType 状态
 */
export function updateClueStatus(clueId: string | number, clueType: number) {
  return post('/cms/workspace/update/clue-status', {
    clueId,
    clueType,
  });
}

// 定义返回数据的类型
export interface CustomerDetail {
  // 线索核心信息
  clueInfoVo: {
    id: string;
    name: string; // 客户姓名
    headImg: string;
    createTime: string; // 创建时间
    triageTime: string; // 分发时间
    followStatus: clueStatusEnum;
    mobileNo: string;
    sex: sexEnum;
    licensePlateNumber: string;
    provinceName: string;
    cityName: string;
    intent: intentionEnum;
    tagList: string[];
    mediaPlatformSource: string; // 线索来源
    communicationStatus: number | null; // 通讯状态
    addWeChat: YesNoEnum; // 是否加微
    followStatusIndex: FollowUpNodeStatusEnum;
  };

  // 贷款申请材料
  loanApplicationMaterials: {
    personalInfo: {
      email: string;
      educationLevel: educationLevelEnum;
      maritalStatus: maritalStatusEnum;
      childCount: number;
      residenceAddress: string;
      houseType: houseTypeEnum;
      company: string;
      companyAddress: string;
      companyPhone: string;
      companyNature: companyNatureEnum;
      monthlyIncome: monthlyIncomeEnum;
      contactName: string;
      contactPhone: string;
      contactRelation: contactRelationEnum;
      threeElementsStatus: CheckStatusEnum; // 三要素校验状态
    };
    vehicleInfo: {
      bodyColor: string;
      interiorColor: string;
      licenseCityName: string;
      factoryDate: string;
      transferCount: number;
      vehicleStatus: VehicleStatusEnum; // 车辆状态
      carOfPersonStatus: CheckStatusEnum; // 人车校验状态
    };
    certificateInfo: {
      idCardImgUrl: string;
      idCardImgBackUrl: string;
      drivingLicenseImgUrl: string;
      drivingLicenseImgBackUrl: string;
      paperDriverLicenseImgUrl: string;
      paperDriverLicenseImgBackUrl: string;
      bankCardImgUrl: string;
      bankCardBackUrl: string;
    };
    otherDocuments: {
      vehiclePhotos: string[];
      contactListMedias: string[];
      traffic12123Medias: string[];
      bankStatements: string[];
      bankAccountInfos: string[];
      insurances: string[];
    };
  };

  salesOpportunities: SalesOpportunity[];
  loanOrderRecords: LoanOrderRecord[];
  clueFollowUpRecords: ClueFollowUpRecord[];
  // followUpNodeStatus: FollowUpNodeStatusEnum;
  // nextFollowUpTime: number;
}

export interface SalesOpportunity {
  saleOpportunityNo: string; // 销售机会ID
  managementCode: string; // 可进件资方
  productName: string; // 可进件产品
  productRate: string; // 产品利率
  loanAmount: number; // 可贷金额（万）
  matchTime: string; // 匹配时间
}

export interface LoanOrderRecord extends UpdateOrderStatusPayloadExt {
  innerOrderNo: string; // 订单ID
  managementCode: string; // 进件资方
  productName: string; // 进件产品
  productRate: string; // 产品利率
  orderNode: string; // 订单节点
  status: number; // 订单状态
  failMessage: string; // 未通过原因
  createTime: number; // 订创建时间
  updateTime: number; // 订单更新时间
}

/**
 * @description: 跟进记录
 */
export interface ClueFollowUpRecord {
  id: number;
  createByName: string; // 创建人
  createTime: number; // 跟进时间
  followUpType: followUpMethodEnum; // 跟进方式
  communicationStatus: communicationStatusEnum; // 沟通状态
  remark: string; // 备注
}
