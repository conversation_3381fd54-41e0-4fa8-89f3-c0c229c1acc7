import { BasicColumn } from '@/components/Table';
import { UserStatusMap } from '@/enums';
import dayjs from 'dayjs';

export interface ListData {
  id: number;
  name: string;
  mobileNo: string;
  roleId: number;
  directSuperiorStr: string;
  locked: number;
  createByStr: string;
  createTime: number;
  updateTime: number;
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: '用户ID',
    key: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '用户姓名',
    key: 'username',
    align: 'center',
  },
  {
    title: '手机号码',
    key: 'mobileNo',
    align: 'center',
  },
  {
    title: '角色',
    key: 'roleName',
    align: 'center',
  },
  {
    title: '直属上级',
    key: 'directSuperiorStr',
    align: 'center',
  },
  {
    title: '状态',
    key: 'locked',
    align: 'center',
    render: (record: ListData) => {
      return <span>{UserStatusMap[record.locked]}</span>;
    },
  },
  {
    title: '创建人',
    key: 'createByStr',
    align: 'center',
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 180,
    render: (record: ListData) => {
      return (
        <span>
          {record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '更新时间',
    key: 'updateTime',
    align: 'center',
    width: 180,
    render: (record: ListData) => {
      return (
        <span>
          {record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
];
