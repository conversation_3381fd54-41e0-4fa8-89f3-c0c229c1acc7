/**
 * 表格工具函数
 */
import { QuestionCircleOutlined } from '@vicons/antd';

export interface ColumnInsertion {
  position: number;
  columns: any[];
}

/**
 * 在指定位置插入列
 * @param baseColumns 基础列配置
 * @param insertions 插入配置数组
 * @returns 插入后的列配置
 */
export function insertColumnsAtPositions(baseColumns: any[], insertions: ColumnInsertion[]): any[] {
  const result = [...baseColumns];

  // 按位置从大到小排序，避免插入时位置偏移
  const sortedInsertions = insertions.sort((a, b) => b.position - a.position);

  sortedInsertions.forEach(({ position, columns: columnsToInsert }) => {
    result.splice(position, 0, ...columnsToInsert);
  });

  return result;
}

/**
 * 在指定位置插入单个列
 * @param baseColumns 基础列配置
 * @param position 插入位置
 * @param column 要插入的列
 * @returns 插入后的列配置
 */
export function insertColumnAtPosition(baseColumns: any[], position: number, column: any): any[] {
  return insertColumnsAtPositions(baseColumns, [{ position, columns: [column] }]);
}

/**
 * 在多个位置插入列的便捷方法
 * @param baseColumns 基础列配置
 * @param positions 位置数组，每个元素包含位置和列配置
 * @returns 插入后的列配置
 */
export function insertColumnsAt(
  baseColumns: any[],
  ...positions: Array<{ at: number; column: any }>
): any[] {
  const insertions = positions.map(({ at, column }) => ({
    position: at,
    columns: [column],
  }));

  return insertColumnsAtPositions(baseColumns, insertions);
}

/**
 * 创建序号列
 * @param options 配置选项
 * @returns 序号列配置
 */
export function createIndexColumn(
  options: {
    title?: string;
    key?: string;
    width?: number;
    align?: 'left' | 'center' | 'right';
  } = {}
) {
  const { title = '序号', key = 'index', width = 60, align = 'center' } = options;

  return {
    title,
    key,
    width,
    align,
    render: (_record: any, index: number) => {
      return index + 1;
    },
  };
}

/**
 * table表头提示
 */
export function createTitleWithTooltip(
  options: {
    title?: string;
    tooltip?: string;
  } = {}
) {
  const { title = '', tooltip = '' } = options;

  const titleFunction = () => {
    return (
      <n-tooltip trigger="hover">
        {{
          trigger: () => (
            <span class="flex items-center">
              {title}
              <n-icon size="18" class="ml-1 text-gray-400 cursor-pointer">
                <QuestionCircleOutlined />
              </n-icon>
            </span>
          ),
          default: () => tooltip,
        }}
      </n-tooltip>
    );
  };

  // 添加displayTitle属性，方便在列设置中获取原始标题
  (titleFunction as any).displayTitle = title;

  return titleFunction;
}
