<template>
  <BaseModal
    v-model:show="showModal"
    title="标记跟进状态"
    :width="500"
    positive-text="保存"
    :on-confirm="handleStatusSubmit"
  >
    <n-form
      ref="statusFormRef"
      :model="statusForm"
      :rules="statusRules"
      label-placement="left"
      :label-width="80"
    >
      <n-form-item path="clueType" label="状态">
        <RadioButtonPicker
          v-model="statusForm.clueType"
          :options="clueTypeOptions"
          name="status-radio-group"
        />
      </n-form-item>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import type { FormInst, FormRules } from 'naive-ui';
  import { NForm, NFormItem } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import RadioButtonPicker from '@/components/Form/src/components/RadioButtonPicker.vue';
  import { clueTypeOptions, clueTypeEnum } from '@/enums/detailEnum';
  import { updateClueStatus } from '@/api/detail';

  const showModal = defineModel<boolean>('show', { default: false });
  const props = defineProps<{
    clueId: number;
  }>();
  const emit = defineEmits(['submit-success']);

  const statusFormRef = ref<FormInst | null>(null);
  const statusForm = ref<{ clueType: clueTypeEnum | null }>({
    clueType: null,
  });

  const statusRules: FormRules = {
    clueType: {
      required: true,
      type: 'number',
      message: '请选择状态',
      trigger: ['blur', 'change'],
    },
  };

  async function handleStatusSubmit() {
    try {
      await statusFormRef.value?.validate();

      await updateClueStatus(props.clueId, statusForm.value.clueType as clueTypeEnum);
      window.$message.success('状态更新成功');
      emit('submit-success');
      statusForm.value.clueType = null;
      showModal.value = false;
    } catch (error) {
      window.$message.error('状态更新失败，请稍后重试');
      return false;
    }
  }
</script>
