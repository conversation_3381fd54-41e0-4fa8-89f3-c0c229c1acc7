// 系统管理接口
import { get, post, put } from '@/utils/lib/axios.package';
import qs from 'qs';

// 获取系统参数list
export function getSystemConfigListApi(params) {
  return get('/cms/systemParam/page', params);
}
// 编辑系统参数
export function saveSystemConfigApi(params) {
  return post('/cms/systemParam/updateById', params);
}
// 获取系统日志list
export function getSystemLogListApi(params) {
  return get('/cms/syslog/page', params);
}
// 导出系统日志
export function exportSystemLogApi(params) {
  return `${import.meta.env.VITE_GLOB_API_URL}/cms/syslog/export?${qs.stringify(params)}`;
}
// 获取管理用户列表
export function getSystemUserListApi(params) {
  return get('/cms/admin/list', params);
}
// 获取部门树
export function getSystemDeptTreeApi() {
  return get('/cms/admin/branch/tree');
}
// 添加、编辑部门
export function saveSystemDeptApi(params) {
  return post('/cms/admin/branch/addOrUpdate', params);
}
// 获取部门详情
export function getSystemDeptDetailApi(params) {
  return get('/cms/admin/branch/detail', params);
}
// 删除部门
export function deleteSystemDeptApi(branchId) {
  return put(`/cms/admin/branch/delete/${branchId}`);
}
// 获取可以配置部门主管用户
export function getSystemDeptUserApi(params) {
  return get('/cms/admin/list/branchManger', params);
}

// 添加管理员
export function addUser(params) {
  return post('/cms/admin/add', params);
}
// 更新用户
export function editUser(params) {
  return put('/cms/admin/update', params);
}
//启用禁用用户
export function enableUser(id) {
  return put(`/cms/admin/enable/${id}`);
}
//新增用户时 可以选择的上级用户
export function getDirectSuperior(params) {
  return get(`/cms/admin/list/branchId`, params);
}
// 移交线索
export function transferClueApi(params) {
  return post('/cms/self-clue/extend-clue', params);
}
// 释放线索
export function releaseClueApi(params) {
  return post('/cms/self-clue/release-clue', params);
}
