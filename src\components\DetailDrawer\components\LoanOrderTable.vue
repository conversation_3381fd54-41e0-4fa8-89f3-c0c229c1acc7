<template>
  <n-data-table
    :columns="columns"
    :data="data"
    :pagination="{ pageSize: 15 }"
    :scroll-x="2000"
    max-height="560px"
    :render-cell="renderCell"
  />
  <BaseModal
    v-model:show="showModal"
    title="更新订单状态"
    :width="600"
    positive-text="保存"
    :on-confirm="handleConfirm"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="orderNode" label="订单节点">
        <n-select v-model:value="model.orderNode" :options="orderNodeOptions" />
      </n-form-item>
      <n-form-item path="nodeStatus" label="订单状态">
        <n-select v-model:value="model.nodeStatus" :options="orderStatusOptions" />
      </n-form-item>
      <!-- 动态表单项 -->
      <template v-if="model.nodeStatus === orderStatusEnum.PASS">
        <!-- 预审 -->
        <template v-if="model.orderNode === orderNodeEnum.PRE_AUDIT">
          <n-form-item label="预审链接" path="ext.signUrl">
            <n-input v-model:value="model.ext.signUrl" placeholder="请输入">
              <!-- <template #prefix>https://</template> -->
            </n-input>
          </n-form-item>
        </template>
        <!-- 授信 -->
        <template v-if="model.orderNode === orderNodeEnum.CREDIT">
          <n-form-item label="进件产品" path="ext.productName">
            <n-input v-model:value="model.ext.productName" placeholder="请输入" />
          </n-form-item>
          <n-form-item label="授信金额" path="ext.creditLine">
            <n-input v-model:value="model.ext.creditLine" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
          <n-form-item label="授信利率" path="ext.productRate">
            <n-input v-model:value="model.ext.productRate" placeholder="请输入">
              <template #suffix>%</template>
            </n-input>
          </n-form-item>
          <n-form-item label="授信期数" path="ext.term">
            <n-input v-model:value="model.ext.term" placeholder="请输入">
              <template #suffix>期</template>
            </n-input>
          </n-form-item>
        </template>
        <!-- 放款 -->
        <template v-if="model.orderNode === orderNodeEnum.LOAN">
          <n-form-item label="进件产品" path="ext.productName">
            <n-input v-model:value="model.ext.productName" placeholder="请输入" />
          </n-form-item>
          <n-form-item label="放款金额" path="ext.loanAmount">
            <n-input v-model:value="model.ext.loanAmount" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
          <n-form-item label="月供" path="ext.monthlyPayment">
            <n-input v-model:value="model.ext.monthlyPayment" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
        </template>
      </template>
      <n-form-item
        v-if="model.nodeStatus === orderStatusEnum.REJECT"
        path="remark"
        label="未通过原因"
      >
        <n-input
          v-model:value="model.remark"
          type="textarea"
          placeholder="请输入未通过原因"
          :autosize="{ minRows: 3 }"
          maxlength="200"
          show-count
        />
      </n-form-item>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { h, ref, reactive, withDirectives, resolveDirective } from 'vue';
  import {
    NDataTable,
    NButton,
    NForm,
    NFormItem,
    NSelect,
    NInput,
    NTime,
    type FormInst,
    type FormRules,
  } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import type { DataTableColumns } from 'naive-ui';
  import type { LoanOrderRecord } from '@/api/detail';
  import {
    orderNodeOptions,
    orderStatusEnum,
    orderStatusOptions,
    orderNodeEnum,
  } from '@/enums/detailEnum';
  import { updateOrderStatus } from '@/api/detail';
  import type { UpdateOrderStatusParams } from '@/api/detail';

  withDefaults(
    defineProps<{
      data: LoanOrderRecord[];
    }>(),
    {}
  );

  const permission = resolveDirective('permission');

  const emit = defineEmits(['update-success']);
  const formRef = ref<FormInst | null>(null);

  const showModal = ref(false);

  // 初始表单状态
  const initialFormState: UpdateOrderStatusParams = {
    innerOrderNo: '',
    orderNode: '',
    nodeStatus: null,
    remark: '',
    ext: {
      signUrl: undefined,
      productName: undefined,
      creditLine: undefined,
      productRate: undefined,
      term: undefined,
      loanAmount: undefined,
      monthlyPayment: undefined,
    },
  };

  const model = reactive<UpdateOrderStatusParams>({ ...initialFormState });

  const validateNumber = (fieldName: string) => {
    return (_rule, value) => {
      if (!value && value !== 0) {
        return new Error(`请输入${fieldName}`);
      }
      const num = Number(value);
      return !isNaN(num) ? true : new Error(`请输入有效数字`);
    };
  };

  const rules: FormRules = {
    orderNode: { required: true, message: '请选择订单节点', trigger: 'change' },
    nodeStatus: { required: true, type: 'number', message: '请选择订单状态', trigger: 'change' },
    remark: {
      required: true,
      message: '请输入未通过原因',
      trigger: ['blur', 'input'],
    },
    'ext.signUrl': { required: true, message: '请输入预审链接', trigger: ['blur', 'input'] },
    'ext.productName': { required: true, message: '请输入进件产品', trigger: ['blur', 'input'] },
    'ext.creditLine': {
      required: true,
      message: '请输入授信金额',
      validator: validateNumber('授信金额'),
      trigger: ['blur', 'input'],
    },
    'ext.productRate': {
      required: true,
      message: '请输入授信利率',
      validator: validateNumber('授信利率'),
      trigger: ['blur', 'input'],
    },
    'ext.term': {
      required: true,
      message: '请输入授信期数',
      validator: validateNumber('授信期数'),
      trigger: ['blur', 'input'],
    },
    'ext.loanAmount': {
      required: true,
      message: '请输入放款金额',
      validator: validateNumber('放款金额'),
      trigger: ['blur', 'input'],
    },
    'ext.monthlyPayment': {
      required: true,
      message: '请输入月供',
      validator: validateNumber('月供'),
      trigger: ['blur', 'input'],
    },
  };

  function openUpdateStatusModal(row: LoanOrderRecord) {
    // 重置表单
    Object.assign(model, JSON.parse(JSON.stringify(initialFormState)));
    // 填充表单
    model.innerOrderNo = row.innerOrderNo;
    model.orderNode = row.orderNode;
    model.nodeStatus = row.status;
    model.remark = row.failMessage;
    model.ext = {
      signUrl: row.signUrl,
      productName: row.productName,
      creditLine: row.creditLine,
      productRate: row.productRate,
      term: row.term,
      loanAmount: row.loanAmount,
      monthlyPayment: row.monthlyPayment,
    };
    showModal.value = true;
  }

  async function handleConfirm() {
    try {
      await formRef.value?.validate();

      await updateOrderStatus(model);

      window.$message.success('更新成功');
      emit('update-success');

      showModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }

  const createColumns = (): DataTableColumns<LoanOrderRecord> => [
    {
      title: '订单ID',
      key: 'innerOrderNo',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      align: 'center',
    },
    { title: '进件资方', key: 'managementCode', width: 100, align: 'center' },
    { title: '进件产品', key: 'productName', width: 100, align: 'center' },
    { title: '产品利率(%)', key: 'productRate', width: 110, align: 'center' },
    { title: '可贷金额(万)', key: 'loanAmount', width: 110, align: 'center' },
    {
      title: '授信金额(元)',
      key: 'creditLine',
      width: 120,
      align: 'center',
    },
    {
      title: '授信期数(期)',
      key: 'term',
      width: 120,
      align: 'center',
    },
    {
      title: '授信利率(%)',
      key: 'productRate',
      width: 120,
      align: 'center',
    },
    {
      title: '实际贷款金额(元)',
      key: 'loanAmount',
      width: 140,
      align: 'center',
    },
    {
      title: '月供(元)',
      key: 'monthlyPayment',
      width: 120,
      align: 'center',
    },
    {
      title: '订单节点',
      key: 'orderNode',
      width: 120,
      align: 'center',
      render(row) {
        return orderNodeOptions.find((opt) => opt.value === row.orderNode)?.label || '--';
      },
    },
    {
      title: '订单状态',
      key: 'status',
      width: 100,
      align: 'center',
      render(row) {
        return orderStatusOptions.find((opt) => opt.value === row.status)?.label || '--';
      },
    },
    { title: '未通过原因', key: 'failMessage', width: 120, align: 'center' },
    {
      title: '订单创建时间',
      key: 'createTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.createTime) });
      },
    },
    {
      title: '订单更新时间',
      key: 'updateTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.updateTime) });
      },
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 100,
      align: 'center',
      render(row) {
        return withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => openUpdateStatusModal(row),
            },
            { default: () => '更新状态' }
          ),
          [[permission, { action: `detail_status` }]]
        );
      },
    },
  ];

  const columns = createColumns();

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
</script>

<style scoped>
  .w-full {
    width: 100%;
  }
</style>
