<template>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      ref="action"
      :actionColumn="actionColumn"
      :scroll-x="1090"
      :striped="true"
    />
  </n-card>

  <FormModal ref="formModal" :form-data="formData" @close="reloadTable" />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { reactive, useTemplateRef, ref } from 'vue';
  import FormModal from './FormModal.vue';
  import { getSystemConfigListApi } from '@/api/system';

  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  // 当前表单数据
  const formData = ref();
  const formModalRef = useTemplateRef<InstanceType<typeof FormModal>>('formModal');
  const actionColumn = reactive({
    width: 120,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-button
          type="primary"
          text
          on-click={() => {
            handleOpenForm(record);
          }}
        >
          编辑
        </n-button>
      );
    },
  });

  const loadDataTable = async (res) => {
    const { data } = await getSystemConfigListApi({ ...res });

    return data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  const handleOpenForm = (row) => {
    formData.value = row;
    formModalRef.value!.formDialogModel = true;
  };
</script>
