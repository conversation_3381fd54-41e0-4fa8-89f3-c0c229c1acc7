{"name": "naive-ui-admin", "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/jekip/naive-ui-admin"}, "private": true, "scripts": {"bootstrap": "pnpm install", "serve": "pnpm run dev", "dev": "vite", "build:test": "vite build --mode test && esno ./build/script/postBuild.ts", "build:prod": "vite build --mode production && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && pnpm run build", "report": "cross-env REPORT=true pnpm run build", "preview": "pnpm run build && vite preview", "preview:dist": "vite preview", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "deploy": "gh-pages -d dist", "lint:eslint": "eslint \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:pretty": "pretty-quick --staged"}, "dependencies": {"@vicons/antd": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.13.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^9.13.0", "axios": "^1.10.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-resize-detector": "^1.2.4", "esno": "^4.8.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "mockjs": "^1.1.0", "naive-ui": "^2.39.0", "pinia": "^2.2.2", "qs": "^6.13.0", "vue": "^3.5.5", "vue-router": "^4.4.5", "vue-types": "^4.2.1"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@faker-js/faker": "^9.0.0", "@types/lodash": "^4.17.7", "@types/node": "^18.19.50", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^3.2.0", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vue/compiler-sfc": "^3.5.5", "@vue/eslint-config-typescript": "^11.0.3", "autoprefixer": "^10.4.20", "axios-mock-adapter": "^2.1.0", "chalk": "^4.1.2", "commitizen": "^4.3.0", "core-js": "^3.38.1", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "1.12.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.28.0", "gh-pages": "^4.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-loader": "^11.1.4", "lint-staged": "^13.3.0", "postcss": "^8.4.45", "prettier": "^2.8.8", "pretty-quick": "^3.3.1", "rimraf": "^3.0.2", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.7.0", "tailwindcss": "^3.4.11", "typescript": "^4.9.5", "unplugin-vue-components": "^0.22.12", "vite": "^5.4.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-style-import": "^2.0.0", "vue-demi": "^0.13.11", "vue-draggable-next": "^2.2.1", "vue-eslint-parser": "^9.4.3", "vuedraggable": "^4.1.0"}, "lint-staged": {"*.{vue,js,ts,tsx}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "keywords": ["vue", "naive-ui", "naive-ui-admin", "vue3", "ts", "tsx", "admin", "typescript"], "repository": {"type": "git", "url": "git+https://github.com/jekip/naive-ui-admin.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jekip/naive-ui-admin/issues"}, "homepage": "https://github.com/jekip/naive-ui-admin", "engines": {"node": ">=16"}}