import type { FormItemRule } from 'naive-ui';
// 表单校验规则

// 手机号校验
export const validPhone = (_rule: FormItemRule, value: any): Error | boolean => {
  if (!value) {
    return new Error('手机号不能为空');
  }
  const reg =
    /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/;
  if (!reg.test(value)) {
    return new Error('请输入正确的手机号');
  } else {
    return true;
  }
};
// 身份证号校验
export const validIdCard = (_rule: FormItemRule, value: any): Error | boolean => {
  if (!value) {
    return new Error('身份证号不能为空');
  }
  const reg =
    /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
  if (!reg.test(value)) {
    return new Error('请输入正确的身份证号');
  } else {
    return true;
  }
};
// 邮箱校验
export const validEmail = (_rule: FormItemRule, value: any): Error | boolean => {
  if (!value) {
    return new Error('邮箱不能为空');
  }
  const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (!reg.test(value)) {
    return new Error('请输入正确的邮箱');
  } else {
    return true;
  }
};
// https校验
export const validHttps = (_rule: FormItemRule, value: any): Error | boolean => {
  if (!value) {
    return new Error('地址不能为空');
  }

  const reg = /^https?:\/\/[\w.-]+(?:\.[\w.-]+)+[/#?]?.*$/;
  if (!reg.test(value)) {
    return new Error('请输入正确的地址');
  } else {
    return true;
  }
};
// 密码校验，最少8位,不允许输入空格，数字、特殊字符、大小写字母都可以,最大60位
export const validPassword = (_rule: FormItemRule, value: any): Error | boolean => {
  if (!value) {
    return new Error('密码不能为空');
  }
  const reg = /^[^\s]{8,60}$/;
  if (!reg.test(value)) {
    return new Error('请输入至少8位密码，不能包含空格');
  } else {
    return true;
  }
};
